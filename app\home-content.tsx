"use client"

import Fancy<PERSON>ard from "@/components/fancy-card"
import <PERSON>Header from "@/components/section-header"
import SingleLanguageFancyCard from "@/components/single-language-fancy-card"
import SingleLanguageSectionHeader from "@/components/single-language-section-header"
import { Building2, Globe2, ChevronRight, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import AnimatedHero from "@/components/animated-hero"
import ChamberOverview from "@/components/chamber-overview"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

const features = [
  {
    icon: <Building2 className="w-6 h-6" />,
    titleKey: "features.chamberActivities.title" as const,
    descriptionKey: "features.chamberActivities.description" as const,
  },
  {
    icon: <Globe2 className="w-6 h-6" />,
    titleKey: "features.internationalRelations.title" as const,
    descriptionKey: "features.internationalRelations.description" as const,
  },
  {
    icon: <Users className="w-6 h-6" />,
    titleKey: "features.memberServices.title" as const,
    descriptionKey: "features.memberServices.description" as const,
  },
]

const stats = [
  {
    number: "85+",
    title: { zh: "年會歷史", en: "Years of History" },
  },
  {
    number: "1000+",
    title: { zh: "企業會員", en: "Corporate Members" },
  },
  {
    number: "200+",
    title: { zh: "年度活動", en: "Annual Events" },
  },
]

export default function HomeContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  return (
    <div className="min-h-screen">

      <AnimatedHero
        title="hero.title"
        description="hero.description"
        image="/placeholder.svg"
        height="large"
        lang={searchParams.get('lang') || 'en'}
      />
      {/* Chamber Overview Section */}
      <ChamberOverview />

      {/* Features Section */}
      <section className="container py-16 md:py-24">
        <SingleLanguageSectionHeader
          title={t('features.title')}
          description={t('features.description')}
          align="center"
        />

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, i) => (
            <SingleLanguageFancyCard
              key={i}
              icon={feature.icon}
              title={t(feature.titleKey)}
              description={t(feature.descriptionKey)}
            />
          ))}
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-[#1E1B4B] text-white py-16">
        <div className="container">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold mb-2">85+</div>
              <div className="text-lg text-white/80">
                {t('footer.yearsOfHistory')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold mb-2">1000+</div>
              <div className="text-lg text-white/80">
                {t('footer.corporateMembers')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold mb-2">200+</div>
              <div className="text-lg text-white/80">
                {t('footer.annualEvents')}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
