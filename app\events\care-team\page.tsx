'use client'

import { useState, useEffect, useMemo, Suspense, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchBloodDonations, type BloodDonation } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import EnhancedMediaSlider from '@/components/enhanced-media-slider'
import { useTranslation, getLanguageFromParams } from '@/lib/translations'

// Define media item interface for enhanced media slider
interface CareTeamMediaItem {
  id: number
  url: string
  mime?: string
  previewUrl?: string | null
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: {
    large?: { url: string }
    medium?: { url: string }
    small?: { url: string }
    thumbnail?: { url: string }
  }
}

// Component that uses searchParams
function CareTeamContent() {
  const [activities, setActivities] = useState<BloodDonation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  const toggleExpanded = useCallback((id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }, [])

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = useCallback((content: string | undefined) => {
    return content && content.length > 100;
  }, [])

  // Helper function to get activity media items compatible with EnhancedMediaSlider
  const getActivityMedia = useCallback((activity: BloodDonation): CareTeamMediaItem[] => {
    const mediaItems: CareTeamMediaItem[] = [];
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

    if (activity.image && Array.isArray(activity.image)) {
      activity.image.forEach((img) => {
        if (img && img.url) {
          mediaItems.push({
            id: img.id,
            url: img.url.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
            mime: img.mime || 'image/jpeg',
            previewUrl: img.previewUrl,
            alternativeText: img.alternativeText,
            caption: img.caption,
            width: img.width,
            height: img.height,
            formats: img.formats
          });
        }
      });
    }

    return mediaItems;
  }, []);

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Fetching care team activities for language:', currentLang);

        const activitiesData = await fetchBloodDonations({
          populate: '*',
          locale: getStrapiLocale
        });

        console.log('Fetched care team activities:', activitiesData);

        if (isMounted) {
          if (activitiesData && activitiesData.length > 0) {
            setActivities(activitiesData);
            setError(null);
          } else {
            setActivities([]);
            setError(null); // Don't set error for empty data
          }
        }
      } catch (err: any) {
        if (isMounted) {
          console.error('Error fetching care team activities:', err);
          setError(t('careTeam.error'));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [currentLang, getStrapiLocale, t])

  if (error) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title={{
            zh: t('careTeam.title'),
            en: t('careTeam.title'),
          }}
          description={{
            zh: t('careTeam.description'),
            en: t('careTeam.description'),
          }}
          image="/placeholder.svg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {error}
            </h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: t('careTeam.title'),
          en: t('careTeam.title'),
        }}
        description={{
          zh: t('careTeam.description'),
          en: t('careTeam.description'),
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('careTeam.staticTitle')}</h2>
              <p>
                {t('careTeam.staticDescription')}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Care Team Activities Listing */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Care Team Activities Items */}
              {activities.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">{t('careTeam.activitiesTitle')}</span>
                    </h2>
                    <p className="text-muted-foreground">{t('careTeam.activitiesDescription')}</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {activities.map((activity) => {
                      const isExpanded = expandedCards[activity.id]
                      const content = activity.impact_summary || activity.description
                      const showReadMore = shouldShowReadMore(content)
                      const mediaItems = getActivityMedia(activity)

                      return (
                        <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {/* Enhanced Media Slider for multiple images/videos */}
                          {mediaItems.length > 0 ? (
                            <EnhancedMediaSlider
                              media={mediaItems}
                              alt={activity.event_name || t('careTeam.defaultMediaAlt')}
                              interval={3000}
                            />
                          ) : (
                            <div className="relative aspect-video">
                              <Image
                                src="/placeholder.svg"
                                alt={activity.event_name || t('careTeam.defaultMediaAlt')}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{activity.event_name}</h3>
                            <div className="space-y-2 mb-4">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{activity.location}</span>
                              </div>
                              {activity.organizer && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{activity.organizer}</span>
                                </div>
                              )}
                            </div>
                            <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                              {content}
                            </div>

                            {showReadMore && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(activity.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    {t('careTeam.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('careTeam.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    {t('careTeam.noActivities')}
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

// Main component with Suspense wrapper
export default function CareTeamPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: "關愛隊",
            en: "Care Team",
          }}
          description={{
            zh: "了解九龍總商會關愛隊的社會公益活動和社區服務項目",
            en: "Learn about KCC Care Team's social welfare activities and community service projects",
          }}
          image="/placeholder.svg"
        />
        <div className="container py-12">
          <div className="grid md:grid-cols-2 gap-6">
            {Array(4).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  <Skeleton className="absolute inset-0" />
                </div>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    }>
      <CareTeamContent />
    </Suspense>
  )
}
