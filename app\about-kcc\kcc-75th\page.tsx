"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"

interface Kcc75thData {
  id: number;
  documentId: string;
  title: string;
  date?: string;
  content?: {
    type: string;
    children: {
      text: string;
      type: string;
    }[];
  }[];
  order?: string;
  image?: {
    formats?: {
      large?: { url: string };
      medium?: { url: string };
      small?: { url: string };
    };
    url: string;
  };
}

export default function Kcc75th() {
  const [anniversaryData, setAnniversaryData] = useState<Kcc75thData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    async function fetchData() {
      try {
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae';

        const response = await fetch(`${strapiUrl}/kcc-75ths?populate=*`, {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log("API Response:", data);

        if (data?.data && Array.isArray(data.data) && data.data.length > 0) {
          // Map all anniversary data
          const allItems = data.data.map((item: any) => ({
            id: item.id,
            documentId: item.documentId,
            title: item.title,
            date: item.date,
            content: item.content,
            image: item.image ? {
              formats: item.image.formats,
              url: item.image.url
            } : undefined
          }));
          setAnniversaryData(allItems);
          setDebugInfo(`Found ${allItems.length} anniversary items.`);
        } else {
          throw new Error("No data found");
        }
      } catch (error: any) {
        setError(error.message);
        console.error("Error fetching KCC 75th data:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const renderContent = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p>No content available</p>;
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        );
      }
      return null;
    });
  };

  const getImageUrl = (item: Kcc75thData) => {
    if (!item.image) return "/placeholder.svg";
    return (
      item.image.formats?.large?.url ||
      item.image.formats?.medium?.url ||
      item.image.url ||
      "/placeholder.svg"
    );
  };

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "龍總七十五週年介紹",
          en: "KCC 75th Anniversary",
        }}
        description={{
          zh: "慶祝我們的七十五週年",
          en: "Celebrating our 75th anniversary",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">龍總七十五週年 / KCC 75th Anniversary</h2>
              <p>
                九龍總商會成立七十五週年是一個重要的里程碑，標認著商會多年來為香港商界和社會發展所做的貢獻。
                在這個特別的時刻，我們回顧過去的成就，也展望未來的發展。
                九龍總商會將繼續秀承其使命，為會員和香港共同建設更美好的未來。
              </p>
              <p>
                The 75th anniversary of the Kowloon Chamber of Commerce is an important milestone, marking the Chamber's contributions to Hong Kong's business community and social development over the years.
                At this special moment, we look back on past achievements and look forward to future developments.
                The Kowloon Chamber of Commerce will continue to fulfill its mission and build a better future for its members and Hong Kong.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardContent className="p-8">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            ) : anniversaryData.length > 0 ? (
              <div className="space-y-16">
                {anniversaryData.map((item, index) => (
                  <div key={item.id} className="prose prose-lg max-w-none">
                    {index > 0 && <hr className="my-8" />}
                    <h1 className="text-3xl font-bold mb-2">{item.title}</h1>

                    <div className="grid md:grid-cols-2 gap-8 items-start">
                      <div className="space-y-4">
                        {item.date && (
                          <p className="text-lg text-muted-foreground">
                            <strong>Date:</strong> {item.date}
                          </p>
                        )}

                        {item.content && renderContent(item.content)}
                      </div>
                      <div className="relative aspect-video mt-6 md:mt-0">
                        <Image
                          src={getImageUrl(item)}
                          alt={`KCC 75th Anniversary - ${item.title}`}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No data available</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}