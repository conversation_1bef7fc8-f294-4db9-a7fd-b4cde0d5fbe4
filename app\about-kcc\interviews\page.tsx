"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Skeleton } from "@/components/ui/skeleton"

export default function InterviewsRedirect() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the correct URL
    router.push("/about-kcc/kcc-interviews")
  }, [router])

  // Show loading state while redirecting
  return (
    <div className="min-h-screen">
      <div className="container py-12">
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <p className="text-muted-foreground text-center mt-8">Redirecting to interviews page...</p>
        </div>
      </div>
    </div>
  )
} 