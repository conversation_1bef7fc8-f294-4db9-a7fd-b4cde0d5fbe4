// Strapi response types
export interface StrapiResponse<T> {
  data: {
    id: number
    attributes: T
  }[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export interface StrapiImage {
  data: {
    id: number
    attributes: {
      url: string
      width: number
      height: number
      alternativeText: string
    }
  }
}

// Content type interfaces
export interface Event {
  title: string
  description: string
  date: string
  category: string
  image: StrapiImage
  createdAt: string
  updatedAt: string
}

export interface NewsArticle {
  title: string
  content: string
  publishDate: string
  image: StrapiImage
  createdAt: string
  updatedAt: string
}

export interface BusinessOpportunity {
  title: string
  description: string
  link: string
  image: StrapiImage
  createdAt: string
  updatedAt: string
}

export interface Member {
  name: string
  position: string
  bio: string
  image: StrapiImage
  createdAt: string
  updatedAt: string
}

export interface Publication {
  title: string
  description: string
  file: StrapiImage
  publishDate: string
  createdAt: string
  updatedAt: string
}

