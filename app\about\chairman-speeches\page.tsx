import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Video, FileText, ChevronRight } from "lucide-react"
import Breadcrumb from "@/components/breadcrumb"
import AnimatedHero from "@/components/animated-hero"

// Sample speeches data - in production, this would come from your CMS
const speeches = [
  {
    id: 1,
    title: "2024年新春致辭",
    titleEn: "2024 Chinese New Year Address",
    date: "2024-02-10",
    occasion: "節慶致辭",
    occasionEn: "Festival Address",
    content: "親愛的會員朋友們：農曆新年來臨之際，我謹代表九龍總商會向大家致以最誠摯的新春祝福...",
    contentEn: "Dear members: On the occasion of Chinese New Year, on behalf of the Kowloon Chamber of Commerce...",
    image: "/placeholder.svg",
    hasVideo: true,
    videoUrl: "#",
    pdfUrl: "#",
  },
  {
    id: 2,
    title: "第二十一屆會董就職典禮致辭",
    titleEn: "Speech at the 21st Board of Directors Inauguration Ceremony",
    date: "2024-01-15",
    occasion: "就職典禮",
    occasionEn: "Inauguration Ceremony",
    content: "各位嘉賓、各位會董：今天我很榮幸能夠在此與大家共同見證九龍總商會第二十一屆會董就職典禮...",
    contentEn:
      "Distinguished guests and directors: I am honored to witness with you today the inauguration ceremony...",
    image: "/placeholder.svg",
    hasVideo: true,
    videoUrl: "#",
    pdfUrl: "#",
  },
  {
    id: 3,
    title: "2023年度會員大會致辭",
    titleEn: "2023 Annual General Meeting Address",
    date: "2023-12-20",
    occasion: "週年大會",
    occasionEn: "Annual Meeting",
    content: "各位會員：首先感謝大家在百忙之中抽空出席今天的會員大會。過去一年，在全體會員的支持下...",
    contentEn:
      "Dear members: First of all, thank you for taking time from your busy schedules to attend today's meeting...",
    image: "/placeholder.svg",
    hasVideo: false,
    pdfUrl: "#",
  },
]

const occasions = [
  { id: "all", label: "全部", labelEn: "All" },
  { id: "festival", label: "節慶致辭", labelEn: "Festival Address" },
  { id: "inauguration", label: "就職典禮", labelEn: "Inauguration Ceremony" },
  { id: "annual", label: "週年大會", labelEn: "Annual Meeting" },
]

const breadcrumbItems = [
  { title: { en: "Home", zh: "主頁" }, path: "/" },
  { title: { en: "About KCC", zh: "關於本會" }, path: "/about" },
  { title: { en: "Chairman's Speeches", zh: "會長演講辭" }, path: "/about/chairman-speeches" },
]

export default function ChairmanSpeechesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "會長演講辭",
          en: "Chairman's Speeches",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbItems} />

        {/* Main Content */}
        <div className="mt-8">
          <Tabs defaultValue="all" className="space-y-8">
            <TabsList className="w-full max-w-2xl mx-auto h-auto flex flex-wrap gap-2 bg-transparent p-0">
              {occasions.map((occasion) => (
                <TabsTrigger
                  key={occasion.id}
                  value={occasion.id}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  {occasion.label}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {speeches.map((speech) => (
                <Card key={speech.id} className="overflow-hidden hover:shadow-lg transition-all">
                  <CardContent className="p-0">
                    <div className="grid md:grid-cols-3 gap-0">
                      <div className="relative aspect-video md:aspect-square">
                        <Image
                          src={speech.image || "/placeholder.svg"}
                          alt={speech.title}
                          fill
                          className="object-cover"
                        />
                        {speech.hasVideo && (
                          <div className="absolute inset-0 flex items-center justify-center bg-black/30 hover:bg-black/40 transition-colors group cursor-pointer">
                            <Video className="w-12 h-12 text-white opacity-80 group-hover:opacity-100 transition-opacity" />
                          </div>
                        )}
                      </div>
                      <div className="md:col-span-2 p-6">
                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge variant="secondary" className="bg-primary/10 text-primary hover:bg-primary/20">
                            {speech.occasion} / {speech.occasionEn}
                          </Badge>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <time>{speech.date}</time>
                          </div>
                        </div>

                        <h2 className="text-2xl font-bold mb-2">
                          {speech.title}
                          <span className="block text-base font-normal text-muted-foreground mt-1">
                            {speech.titleEn}
                          </span>
                        </h2>

                        <div className="space-y-4 mb-6">
                          <p className="text-muted-foreground line-clamp-2">{speech.content}</p>
                          <p className="text-muted-foreground line-clamp-2">{speech.contentEn}</p>
                        </div>

                        <div className="flex flex-wrap gap-3">
                          {speech.hasVideo && (
                            <Button variant="outline">
                              <Video className="h-4 w-4 mr-2" />
                              觀看視頻 / Watch Video
                            </Button>
                          )}
                          <Button variant="outline">
                            <FileText className="h-4 w-4 mr-2" />
                            下載全文 / Download PDF
                          </Button>
                          <Button>
                            閱讀更多 / Read More
                            <ChevronRight className="h-4 w-4 ml-2" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            {/* Filter tabs for different occasions */}
            {occasions.slice(1).map((occasion) => (
              <TabsContent key={occasion.id} value={occasion.id}>
                <div className="space-y-6">
                  {speeches
                    .filter((speech) => speech.occasion === occasion.label)
                    .map((speech) => (
                      <Card key={speech.id} className="overflow-hidden hover:shadow-lg transition-all">
                        <CardContent className="p-0">
                          <div className="grid md:grid-cols-3 gap-0">
                            <div className="relative aspect-video md:aspect-square">
                              <Image
                                src={speech.image || "/placeholder.svg"}
                                alt={speech.title}
                                fill
                                className="object-cover"
                              />
                              {speech.hasVideo && (
                                <div className="absolute inset-0 flex items-center justify-center bg-black/30 hover:bg-black/40 transition-colors group cursor-pointer">
                                  <Video className="w-12 h-12 text-white opacity-80 group-hover:opacity-100 transition-opacity" />
                                </div>
                              )}
                            </div>
                            <div className="md:col-span-2 p-6">
                              <div className="flex flex-wrap gap-2 mb-3">
                                <Badge variant="secondary" className="bg-primary/10 text-primary hover:bg-primary/20">
                                  {speech.occasion} / {speech.occasionEn}
                                </Badge>
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Calendar className="h-4 w-4" />
                                  <time>{speech.date}</time>
                                </div>
                              </div>

                              <h2 className="text-2xl font-bold mb-2">
                                {speech.title}
                                <span className="block text-base font-normal text-muted-foreground mt-1">
                                  {speech.titleEn}
                                </span>
                              </h2>

                              <div className="space-y-4 mb-6">
                                <p className="text-muted-foreground line-clamp-2">{speech.content}</p>
                                <p className="text-muted-foreground line-clamp-2">{speech.contentEn}</p>
                              </div>

                              <div className="flex flex-wrap gap-3">
                                {speech.hasVideo && (
                                  <Button variant="outline">
                                    <Video className="h-4 w-4 mr-2" />
                                    觀看視頻 / Watch Video
                                  </Button>
                                )}
                                <Button variant="outline">
                                  <FileText className="h-4 w-4 mr-2" />
                                  下載全文 / Download PDF
                                </Button>
                                <Button>
                                  閱讀更多 / Read More
                                  <ChevronRight className="h-4 w-4 ml-2" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    </div>
  )
}

