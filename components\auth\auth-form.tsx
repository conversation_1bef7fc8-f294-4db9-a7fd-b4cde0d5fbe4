"use client"

import { useState } from "react"
import { toast } from "sonner"
import { useForm, FieldErrors } from "react-hook-form"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/ui/icons"
import { fetchAPI } from "@/strapi"
import { useUser } from "@/contexts/user-context"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

// Define form data types
interface BaseFormData {
  mode: "login" | "register";
  password: string;
}

interface LoginFormData extends BaseFormData {
  mode: "login";
  identifier: string;
}

interface RegisterFormData extends BaseFormData {
  mode: "register";
  username: string;
  email: string;
}

type AuthFormData = LoginFormData | RegisterFormData;

// Validation schemas
const loginSchema = z.object({
  mode: z.literal("login"),
  identifier: z.string().min(3, "請輸入您的用戶名稱或電子郵件 / Please enter your username or email"),
  password: z.string().min(8, "密碼必須至少為8個字符 / Password must be at least 8 characters"),
});

const registerSchema = z.object({
  mode: z.literal("register"),
  username: z.string().min(3, "用戶名稱必須至少為3個字符 / Username must be at least 3 characters"),
  email: z.string().email("無效的電子郵件地址 / Invalid email address"),
  password: z.string().min(8, "密碼必須至少為8個字符 / Password must be at least 8 characters"),
});

const authSchema = z.discriminatedUnion("mode", [loginSchema, registerSchema]);

interface AuthFormProps {
  mode: "login" | "register"
}

export function AuthForm({ mode }: AuthFormProps) {
  const { setUser } = useUser()
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AuthFormData>({
    resolver: zodResolver(authSchema),
    defaultValues: {
      mode
    }
  })

  const router = useRouter()

  async function onSubmit(formData: AuthFormData) {
   setIsLoading(true)

   try {
     const endpoint = mode === "register" ? "auth/local/register" : "auth/local"
     const body = mode === "register"
       ? {
           username: (formData as RegisterFormData).username,
           email: (formData as RegisterFormData).email,
           password: formData.password
         }
       : {
           identifier: (formData as LoginFormData).identifier,
           password: formData.password
         }

     console.log("Sending auth request:", { endpoint, body })
     const response = await fetchAPI(endpoint, {}, {
       method: "POST",
       body: JSON.stringify(body)
     })

      if (response.jwt) {
        localStorage.setItem("token", response.jwt)
        setUser(response.user)
        toast.success(mode === "register" ? "帳戶創建成功！ / Account created successfully!" : "登入成功！ / Logged in successfully!")
        router.push("/dashboard")
      }
    } catch (error: any) {
      console.error(mode === "register" ? "Registration error:" : "Login error:", error)
      console.log('Error details:', {
        status: error?.response?.status,
        data: error?.response?.data,
        message: error?.response?.data?.error?.message
      })

      if (mode === "login") {
        // Always show credential error for login failures
        toast.error("無效的電子郵件/用戶名稱或密碼。請檢查您的憑證並重試。 / Invalid email/username or password. Please check your credentials and try again.");
      } else if (mode === "register") {
        if (error?.response?.data?.error?.message?.toLowerCase().includes('email')) {
          toast.error("此電子郵件已註冊。請使用其他電子郵件。 / This email is already registered. Please use a different email.");
        } else if (error?.response?.data?.error?.message?.toLowerCase().includes('username')) {
          toast.error("此用戶名稱已被使用。請選擇其他用戶名稱。 / This username is already taken. Please choose another username.");
        } else {
          toast.error("此電子郵件或用戶名稱已被使用。請嘗試其他。 / This email or username is already taken. Please try another.");
        }
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-[400px] border-0 shadow-sm">
      <CardHeader className="space-y-3 pb-6">
        <CardTitle className="text-2xl font-bold">
          {mode === "login" ? (
            <>
              <span className="block text-2xl mb-1">登入 / Sign in</span>
            </>
          ) : (
            <>
              <span className="block text-2xl mb-1">註冊帳戶 / Create an account</span>
            </>
          )}
        </CardTitle>
        <CardDescription className="text-base">
          <span className="block mb-1">
            {mode === "login"
              ? "請輸入您的帳號資料以登入 / Enter your credentials to sign in"
              : "請填寫以下資料以創建新帳戶 / Enter your information to create an account"}
          </span>
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="grid gap-4">
          {mode === "register" ? (
            <>
              <div className="grid gap-2">
                <Label htmlFor="username" className="mb-1.5">
                <span className="block font-medium">用戶名稱 / Username</span>
              </Label>
                <Input
                  id="username"
                  type="text"
                  placeholder="請輸入用戶名稱 / username"
                  {...register("username")}
                />
                {mode === "register" && "username" in errors && (
                  <p className="text-sm text-red-500">{errors?.username?.message}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email" className="mb-1.5">
                <span className="block font-medium">電子郵件 / Email</span>
              </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="請輸入電子郵件 / <EMAIL>"
                  {...register("email")}
                />
                {mode === "register" && "email" in errors && (
                  <p className="text-sm text-red-500">{errors?.email?.message}</p>
                )}
              </div>
            </>
          ) : (
            <div className="grid gap-2">
              <Label htmlFor="identifier" className="mb-1.5">
                <span className="block font-medium">用戶名稱或電子郵件 / Username or Email</span>
              </Label>
              <Input
                id="identifier"
                type="text"
                placeholder="請輸入用戶名稱或電子郵件 / Enter your username or email"
                autoComplete="username"
                {...register("identifier")}
              />
              {mode === "login" && "identifier" in errors && (
                <p className="text-sm text-red-500">{errors?.identifier?.message}</p>
              )}
            </div>
          )}
          <div className="grid gap-2">
            <Label htmlFor="password" className="mb-1.5">
              <span className="block font-medium">密碼 / Password</span>
            </Label>
            <div className="flex items-center gap-2">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="請輸入密碼 / Enter your password"
                autoComplete="current-password"
                {...register("password")}
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <Icons.eyeOff className="h-4 w-4" />
                ) : (
                  <Icons.eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password.message}</p>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            className="w-full bg-[#1E1B4B] hover:bg-[#2d2870] text-white py-6"
            type="submit"
            disabled={isLoading}
          >
            {isLoading && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            {mode === "login" ? (
              <>
                登入 / Sign in
              </>
            ) : (
              <>
                註冊 / Create account
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}