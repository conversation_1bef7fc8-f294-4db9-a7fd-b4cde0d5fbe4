"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { submitRegistration } from "@/lib/strapi"
import { toast } from "sonner"

interface FormData {
  Name: string
  email: string
  Number: string
  EventName: string
  OrganizationName: string
}

function RegisterForm() {
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    Name: "",
    email: searchParams.get("email") || "",
    Number: "",
    EventName: searchParams.get("eventName") || "",
    OrganizationName: ""
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      await submitRegistration(formData)
      toast.success("活動報名已成功提交")
      window.location.href = "/events/upcoming"
    } catch (error) {
      console.error("Error submitting registration:", error)
      toast.error("提交報名時發生錯誤，請稍後再試")
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({
      ...prev,
      [id]: value
    }))
  }

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardContent className="p-6">
        <h2 className="text-2xl font-bold mb-6">活動報名 / Event Registration</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="Name" className="font-medium">
              姓名 / Name <span className="text-red-500">*</span>
            </label>
            <Input
              id="Name"
              placeholder="Please enter your name"
              required
              value={formData.Name}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="font-medium">
              電郵 / Email <span className="text-red-500">*</span>
            </label>
            <Input
              id="email"
              type="email"
              placeholder="Please enter your email"
              required
              value={formData.email}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="Number" className="font-medium">
              電話 / Phone Number <span className="text-red-500">*</span>
            </label>
            <Input
              id="Number"
              placeholder="Please enter your phone number"
              required
              value={formData.Number}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="EventName" className="font-medium">
              活動名稱 / Event Name <span className="text-red-500">*</span>
            </label>
            <Input
              id="EventName"
              placeholder="Event name"
              required
              value={formData.EventName}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="OrganizationName" className="font-medium">
              機構名稱 / Organization Name <span className="text-red-500">*</span>
            </label>
            <Input
              id="OrganizationName"
              placeholder="Please enter your organization name"
              required
              value={formData.OrganizationName}
              onChange={handleChange}
            />
          </div>

          <Button
            type="submit"
            className="w-full bg-[#1E1B4B] hover:bg-[#1E1B4B]/90"
            disabled={isLoading}
          >
            {isLoading ? "提交中..." : "提交報名 / Submit Registration"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

export default function RegisterPage() {
  return (
    <div className="container py-12">
      <Suspense fallback={<div>Loading...</div>}>
        <RegisterForm />
      </Suspense>
    </div>
  )
}