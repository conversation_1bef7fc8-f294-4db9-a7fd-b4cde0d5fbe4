"use client"

import { useState, useEffect } from "react"
import { fetchFrom<PERSON>tra<PERSON> } from "@/lib/strapi"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import { Mail, Phone } from "lucide-react"

interface Business {
  id: number
  documentId: string
  title: string
  content: string
  email: string
  phone: number
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: Array<{
    id: number
    documentId: string
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }>
}

interface StrapiResponse {
  data: Business[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export default function BusinessDetailsPage() {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [loading, setLoading] = useState(true)

  // Fallback data in case the API fails
  const fallbackBusinesses: Business[] = [
    {
      id: 1,
      documentId: "fallback-1",
      title: "Local business cooperation",
      content: "Connect local businesses, expand business networks, and find partners",
      email: "<EMAIL>",
      phone: ***********,
      createdAt: "2023-10-20T00:00:00.000Z",
      updatedAt: "2023-10-20T00:00:00.000Z",
      publishedAt: "2023-10-20T00:00:00.000Z",
      locale: "en"
    },
    {
      id: 2,
      documentId: "fallback-2",
      title: "International trade opportunities",
      content: "Explore international markets and learn about the latest trade policies and opportunities",
      email: "<EMAIL>",
      phone: ***********,
      createdAt: "2023-10-20T00:00:00.000Z",
      updatedAt: "2023-10-20T00:00:00.000Z",
      publishedAt: "2023-10-20T00:00:00.000Z",
      locale: "en"
    },
    {
      id: 3,
      documentId: "fallback-3",
      title: "Exhibitions and conferences",
      content: "Participate in various business exhibitions and conferences to showcase products and services",
      email: "<EMAIL>",
      phone: ***********,
      createdAt: "2023-10-20T00:00:00.000Z",
      updatedAt: "2023-10-20T00:00:00.000Z",
      publishedAt: "2023-10-20T00:00:00.000Z",
      locale: "en"
    }
  ]

  useEffect(() => {
    const loadBusinesses = async () => {
      try {
        // Get the token from environment variables
        const STRAPI_TOKEN = process.env.NEXT_PUBLIC_STRAPI_TOKEN || "5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae"

        // Fetch directly from the URL with authorization header
        const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'}/businesses?populate=*`, {
          headers: {
            'Authorization': `Bearer ${STRAPI_TOKEN}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error("API error response:", errorText)
          throw new Error(`API error: ${response.status}`)
        }

        const data = await response.json()
        console.log("Business data fetched successfully:", data)
        setBusinesses(data.data || [])
      } catch (err: any) {
        console.error("Error fetching business data:", err)
        console.log("Using fallback business data instead")
        setBusinesses(fallbackBusinesses)
      } finally {
        setLoading(false)
      }
    }

    loadBusinesses()
  }, [])

  // No error handling needed since we're using fallback data

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "商業機會",
          en: "Business Opportunities",
        }}
        description={{
          zh: "九龍總商會為會員提供多元化商業機會，促進商貿合作與發展",
          en: "Kowloon Chamber of Commerce provides diverse business opportunities for members, promoting business cooperation and development.",
        }}
        image="/placeholder.svg"
        height="medium"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">商業機會 / Business Opportunities</h2>
              <p>
                九龍總商會為會員提供多元化的商業機會，包括本地商業合作、國際貿易機會、展覽會議等。
                透過我們的平台，會員可以拓展商業網絡，尋找合作夥伴，開拓新市場，
                促進商貿合作與發展。
              </p>
              <p>
                The Kowloon Chamber of Commerce provides diverse business opportunities for members, including local business cooperation, international trade opportunities, exhibitions and conferences, etc.
                Through our platform, members can expand their business networks, find partners, explore new markets,
                and promote business cooperation and development.
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            // Loading skeletons
            Array(3).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden flex flex-col h-full">
                <CardContent className="p-0 flex flex-col flex-grow">
                  <div className="h-48 bg-muted" />
                  <div className="p-6 space-y-4 flex-grow flex flex-col">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                    <div className="space-y-2 mt-auto">
                      <Skeleton className="h-2 w-full" />
                      <Skeleton className="h-2 w-5/6" />
                      <Skeleton className="h-2 w-4/6" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            businesses.map((business) => (
              <Card key={business.id} className="overflow-hidden flex flex-col h-full">
                <CardContent className="p-0 flex flex-col flex-grow">
                  {business.image && business.image.length > 0 ? (
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={business.image[0].formats?.medium?.url || business.image[0].formats?.small?.url || business.image[0].url}
                        alt={business.title}
                        width={300}
                        height={250}
                        className="object-cover w-full h-full"
                      />
                    </div>
                  ) : (
                    <div className="h-48 bg-muted flex items-center justify-center">
                      <span className="text-4xl">🏢</span>
                    </div>
                  )}
                  <div className="p-6 space-y-4 flex-grow flex flex-col">
                    <h3 className="text-2xl font-semibold">{business.title}</h3>
                    <p className="text-muted-foreground">{business.content}</p>

                    <div className="space-y-2 pt-2 mt-auto">
                      {business.email && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Mail className="h-4 w-4 mr-2" />
                          <span>{business.email}</span>
                        </div>
                      )}

                      {business.phone && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Phone className="h-4 w-4 mr-2" />
                          <span>{business.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
