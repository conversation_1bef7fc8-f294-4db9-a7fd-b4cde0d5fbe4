"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import FullImageSlider from "@/components/full-image-slider"
import { fetchRentals } from "@/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { MapPin, Phone, Mail, Home } from "lucide-react"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImage {
  id: number
  url: string
  mime?: string
  previewUrl?: string
  alternativeText?: string
  caption?: string
  width?: number
  height?: number
  formats?: {
    large?: StrapiImageFormat
    medium?: StrapiImageFormat
    small?: StrapiImageFormat
    thumbnail?: StrapiImageFormat
  }
}

interface Rental {
  id: number
  documentId: string
  Title: string
  Subtitle: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>
  location: string
  email: string
  number: string
  area: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

interface StrapiResponse {
  data: Rental[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

// Loading component
function RentalLoading() {
  return (
    <div className="container py-12">
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="overflow-hidden">
            <div className="aspect-video relative">
              <Skeleton className="absolute inset-0" />
            </div>
            <CardContent className="p-6">
              <Skeleton className="h-6 w-3/4 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3 mb-4" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Main content component
function RentalContent() {
  const searchParams = useSearchParams()
  const [rentals, setRentals] = useState<Rental[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (itemId: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  const loadRentals = async (page = 1) => {
    try {
      setLoading(true)

      console.log('Rental page - Current language:', currentLang)
      console.log('Rental page - Strapi locale:', getStrapiLocale)

      const response = await fetchRentals({
        populate: "*",
        pagination: { page, pageSize: 6 },
        locale: getStrapiLocale
      }) as StrapiResponse

      if (page === 1) {
        setRentals(response.data)
        setCurrentPage(1)
      } else {
        setRentals(prev => [...prev, ...response.data])
        setCurrentPage(page)
      }

      setHasMore(response.meta.pagination.page < response.meta.pagination.pageCount)
      setError(null)
    } catch (err: any) {
      console.error("Error fetching rentals:", err)
      setError(t('rental.error'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRentals(1)
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Helper function to render structured content with read more functionality
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | undefined, itemId: number, maxLength: number = 150) => {
    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('rental.noContent')}</p>
    }

    // Filter out empty content
    const validContent = content.filter(block => {
      if (block.type === "paragraph") {
        const text = block.children?.map(child => child.text).join('').trim()
        return text && text.length > 0
      }
      return false
    })

    if (validContent.length === 0) {
      return <p className="text-muted-foreground">{t('rental.noContent')}</p>
    }

    const fullText = validContent.map(block =>
      block.children?.map(child => child.text).join('')
    ).join(' ')

    const isExpanded = expandedItems.has(itemId)
    const shouldTruncate = fullText.length > maxLength
    const displayText = shouldTruncate && !isExpanded
      ? fullText.substring(0, maxLength) + '...'
      : fullText

    return (
      <div>
        <p className="mb-2">{displayText}</p>
        {shouldTruncate && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleExpanded(itemId)}
            className="text-primary hover:text-primary/80 p-0 h-auto"
          >
            {isExpanded ? (
              <>
                {t('rental.readLess')} <ChevronUp className="ml-1 h-3 w-3" />
              </>
            ) : (
              <>
                {t('rental.readMore')} <ChevronDown className="ml-1 h-3 w-3" />
              </>
            )}
          </Button>
        )}
      </div>
    )
  }

  // Helper function to get images for ImageSlider
  const getRentalImages = (rental: Rental): StrapiImage[] => {
    const images: StrapiImage[] = []

    // Check if image is an array (multiple images)
    if (Array.isArray(rental.image)) {
      images.push(...rental.image)
    } else if (rental.image) {
      // Single image
      images.push(rental.image)
    }

    // Also check images field
    if (rental.images && Array.isArray(rental.images)) {
      images.push(...rental.images)
    }

    return images
  }

  if (loading && rentals.length === 0) {
    return <RentalLoading />
  }

  if (error) {
    return (
      <div className="container py-12">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">
              <p className="text-red-500">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-12">
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {rentals.map((rental) => {
          const images = getRentalImages(rental)
          return (
            <Card key={rental.id} className="overflow-hidden">
              <div className="aspect-[4/3] relative">
                {images.length > 0 ? (
                  <FullImageSlider
                    images={images}
                    alt={rental.Title || "Rental Property"}
                  />
                ) : (
                  <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                    <p className="text-muted-foreground">{t('rental.noImages')}</p>
                  </div>
                )}
              </div>
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-2">{rental.Title}</h3>
                {rental.Subtitle && renderStructuredContent(rental.Subtitle, rental.id)}
                <div className="mt-4 space-y-2">
                  {rental.location && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="w-4 h-4" />
                      <span>{rental.location}</span>
                    </div>
                  )}
                  {rental.area && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Home className="w-4 h-4" />
                      <span>{rental.area}</span>
                    </div>
                  )}
                  {rental.number && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Phone className="w-4 h-4" />
                      <span>{rental.number}</span>
                    </div>
                  )}
                  {rental.email && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Mail className="w-4 h-4" />
                      <span>{rental.email}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
      {hasMore && (
        <div className="mt-8 text-center">
          <Button
            onClick={() => loadRentals(currentPage + 1)}
            disabled={loading}
          >
            {loading ? t('rental.loading') : t('rental.loadMore')}
          </Button>
        </div>
      )}
    </div>
  )
}

// New client component to handle AnimatedHero and Suspense for main content
function RentalClientPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="rental.title"
        description="rental.description"
        lang={currentLang}
      />
      <Suspense fallback={<RentalLoading />}>
        <RentalContent />
      </Suspense>
    </div>
  )
}

// Main page component using Suspense for the client component
export default function RentalPage() {
  return (
    <Suspense fallback={<RentalLoading />}>
      <RentalClientPage />
    </Suspense>
  )
}