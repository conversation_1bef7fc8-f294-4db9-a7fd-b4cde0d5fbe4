"use client"

import { useState, useEffect, useMemo, Suspense, useCallback } from "react"
import { useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, Users, FileText, CheckCircle, ChevronDown, ChevronUp } from 'lucide-react'
import { fetchMonthlyMeetings, type MonthlyMeeting } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import EnhancedMediaSlider from "@/components/enhanced-media-slider" // Assuming this component exists and is suitable
import { useTranslation, getLanguageFromParams } from "@/lib/translations" // Import necessary translation hooks

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText?: string
  caption?: string
  width: number | null
  height: number | null
  formats?: StrapiImageFormats
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl?: string
  provider: string
  provider_metadata?: any
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale?: string
  isVideo?: boolean
}

// Combined interface for media items compatible with EnhancedMediaSlider
interface MeetingMediaItem {
    id: number;
    url: string;
    mime?: string;
    previewUrl?: string | null;
    alternativeText?: string | null;
    // Include format URLs if EnhancedMediaSlider uses them (check its props)
    formats?: StrapiImageFormats;
}


// Main page component with Suspense
export default function MonthlyMeetingsPage() {
  return (
    <Suspense fallback={<MonthlyMeetingsLoading />}>
      <MonthlyMeetingsPageContent />
    </Suspense>
  );
}

// Component that uses searchParams
function MonthlyMeetingsPageContent() {
  const searchParams = useSearchParams();
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams]);
  const { t } = useTranslation(currentLang);

  const [meetings, setMeetings] = useState<MonthlyMeeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({}); // Use string keys for meetingId-sectionName

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'; // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK'; // Simplified Chinese Hong Kong
      case 'en':
        return 'en'; // English
      default:
        return undefined; // Use default locale
    }
  }, [currentLang]);


  const toggleExpanded = useCallback((key: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  }, []);

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Fetching monthly meetings for language:', currentLang);

        const meetingsData = await fetchMonthlyMeetings({
          populate: '*',
          locale: getStrapiLocale // Pass locale for internationalization
        });

        if (!isMounted) return;

        console.log('Fetched monthly meetings:', meetingsData?.length, 'items');

        if (meetingsData && meetingsData.length > 0) {
           // Sort the meetings by date if available (most recent first)
          const sortedMeetings = [...meetingsData].sort((a, b) => {
            if (!a.dateofmeeting && !b.dateofmeeting) return 0
            if (!a.dateofmeeting) return 1
            if (!b.dateofmeeting) return -1
            return new Date(b.dateofmeeting).getTime() - new Date(a.dateofmeeting).getTime()
          })
          setMeetings(sortedMeetings);
        } else {
           // Fallback without locale if needed, similar to new-era
           try {
            const fallbackData = await fetchMonthlyMeetings({ populate: '*' });

            if (!isMounted) return;

            if (fallbackData && fallbackData.length > 0) {
              const sortedFallbackMeetings = [...fallbackData].sort((a, b) => {
                if (!a.dateofmeeting && !b.dateofmeeting) return 0
                if (!a.dateofmeeting) return 1
                if (!b.dateofmeeting) return -1
                return new Date(b.dateofmeeting).getTime() - new Date(a.dateofmeeting).getTime()
              })
              setMeetings(sortedFallbackMeetings);
            } else {
               setError(t('monthlyMeetings.noMeetings'));
            }
          } catch (fallbackErr) {
            if (isMounted) {
               setError(t('monthlyMeetings.noMeetings'));
            }
          }
        }
      } catch (err: any) {
        if (isMounted) {
          console.error('Error fetching monthly meetings:', err);
          setError(t('monthlyMeetings.error')); // Use translation key for error
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [currentLang, getStrapiLocale]); // Removed 't' from dependencies


   // Helper function to get meeting media items compatible with EnhancedMediaSlider
  const getMeetingMedia = useCallback((meeting: MonthlyMeeting): MeetingMediaItem[] => {
    const mediaItems: MeetingMediaItem[] = [];
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'; // Assume Strapi URL is in env

    if (meeting.image && Array.isArray(meeting.image)) {
       mediaItems.push(...meeting.image.map(img => ({
          id: img.id,
          url: img.url.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
          mime: img.mime,
          previewUrl: img.previewUrl,
          alternativeText: img.alternativeText,
          formats: img.formats
       })));
    } else if (meeting.image && !Array.isArray(meeting.image)) {
        // Handle single image case if Strapi returns it differently
         const singleImage = meeting.image as StrapiImage;
          mediaItems.push({
            id: singleImage.id,
            url: singleImage.url.startsWith('http') ? singleImage.url : `${strapiUrl}${singleImage.url}`,
            mime: singleImage.mime,
            previewUrl: singleImage.previewUrl,
            alternativeText: singleImage.alternativeText,
            formats: singleImage.formats
          });
    }

    // Remove potential duplicates if image and images fields both contain media (adjust if needed)
    const uniqueMedia = mediaItems.filter((item, index, self) =>
      index === self.findIndex(t => t.id === item.id)
    );


    return uniqueMedia;
  }, []);


  // Helper function to render content with expand/collapse
  const renderContent = useCallback((content: string | undefined, key: string, isExpanded: boolean) => {
    if (!content) return null;

    // Check if content is long enough to need truncation
    const isLong = content.length > 150; // Simple character count check

    return (
      <div className="relative"> {/* Added relative positioning */}
        <div className={`text-sm text-muted-foreground ${!isExpanded && isLong ? 'line-clamp-2' : ''}`}>
          {content.split('\n').map((line, index) => (
            <p key={index} className={index > 0 ? 'mt-1' : ''}>
              {line}
            </p>
          ))}
        </div>
        
        {isLong && (
          <Button
            variant="link"
            size="sm"
            onClick={() => toggleExpanded(key)}
            className="mt-2 p-0 h-auto text-primary hover:text-primary/80 font-medium"
          >
            {isExpanded ? (
               // Use the correct translation key from monthlyMeetings namespace
                <>
                  {t('monthlyMeetings.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                </>
            ) : (
               // Use the correct translation key from monthlyMeetings namespace
                <>
                  {t('monthlyMeetings.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                </>
            )}
          </Button>
        )}
      </div>
    );
  }, [toggleExpanded, t]);


  if (error) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title={t('monthlyMeetings.title')}
          description={t('monthlyMeetings.description')}
          image="/placeholder.svg"
          lang={currentLang}
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {t('common.error')}
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={t('monthlyMeetings.title')}
        description={t('monthlyMeetings.description')}
        image="/placeholder.svg"
        lang={currentLang}
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('monthlyMeetings.staticTitle')}</h2>
              <p>{t('monthlyMeetings.staticDescription')}</p>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Meetings Listing */}
        <section>
          {loading ? (
             <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"> {/* Using grid for loading skeletons */}
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Monthly Meetings Items */}
              {meetings.length > 0 ? (
                <div className="space-y-6"> {/* Keep space-y-6 for vertical spacing between cards */}
                  <div className="border-b pb-2 mb-6"> {/* Added mb-6 for space below the header */}
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">{t('monthlyMeetings.activitiesTitle')}</span>
                    </h2>
                    <p className="text-muted-foreground">{t('monthlyMeetings.activitiesDescription')}</p>
                  </div>

                  {/* Changed to grid layout for cards */}
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 items-start"> {/* Added items-start for top alignment */}
                    {meetings.map((meeting) => {
                      const mediaItems = getMeetingMedia(meeting);

                      return (
                        <Card key={meeting.id} className="overflow-hidden hover:shadow-lg transition-all flex flex-col"> {/* Added flex flex-col for internal layout */}
                           {/* Media Section */}
                           {mediaItems.length > 0 ? (
                             mediaItems.length > 1 ? (
                               <EnhancedMediaSlider
                                 media={mediaItems}
                                 alt={meeting.title || t('monthlyMeetings.defaultMediaAlt')} // Use translation key
                                 interval={3000}
                               />
                             ) : (
                               <div className="relative aspect-video w-full min-h-[1px] flex items-center justify-center bg-gray-50 overflow-hidden"> {/* Container for single media */}
                                 {mediaItems[0].mime?.startsWith('video/') ? (
                                   <video
                                     src={mediaItems[0].url}
                                     poster={mediaItems[0].previewUrl || undefined}
                                     controls
                                     className="w-full h-full object-contain"
                                     playsInline
                                   />
                                 ) : (
                                   <Image
                                     src={mediaItems[0].url || '/placeholder.svg'}
                                     alt={meeting.title || t('monthlyMeetings.defaultMediaAlt')} // Use translation key
                                     fill
                                     className="object-contain"
                                     sizes="(max-width: 768px) 100vw, 33vw"
                                   />
                                 )}
                               </div>
                             )
                           ) : (
                             <div className="flex items-center justify-center aspect-video bg-gray-100">
                               <p className="text-muted-foreground p-4 text-center">{t('monthlyMeetings.noMedia')}</p> {/* Use translation key */}
                             </div>
                           )}


                          <CardContent className="p-6 flex flex-col flex-grow"> {/* Added flex flex-col flex-grow for content area */}
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(meeting.dateofmeeting).toLocaleDateString()}</time>
                            </div>
                            {/* Use meeting title instead of hardcoded */}
                            <h3 className="text-xl font-bold mb-2">{meeting.title}</h3>

                            {/* Agenda */}
                            {meeting.agenda && (
                              <div className="space-y-1 mb-3"> {/* Adjusted spacing */}
                                <h4 className="font-semibold text-sm flex items-center gap-2"> {/* Removed mb-1 */}
                                  <FileText className="h-4 w-4" />
                                  {t('monthlyMeetings.agendaTitle')} {/* Use translation key */}
                                </h4>
                                 {renderContent(meeting.agenda, `${meeting.id}-agenda`, expandedSections[`${meeting.id}-agenda`])}
                              </div>
                            )}


                            {/* Special Guests */}
                            {meeting.special_guests && (
                              <div className="space-y-1 mb-3"> {/* Adjusted spacing */}
                                <h4 className="font-semibold text-sm flex items-center gap-2"> {/* Removed mb-1 */}
                                  <Users className="h-4 w-4" />
                                   {t('monthlyMeetings.guestsTitle')} {/* Use translation key */}
                                </h4>
                                {renderContent(meeting.special_guests, `${meeting.id}-guests`, expandedSections[`${meeting.id}-guests`])}
                              </div>
                            )}

                            {/* Decisions Taken */}
                            {meeting.decisions_taken && (
                              <div className="space-y-1"> {/* Adjusted spacing */}
                                <h4 className="font-semibold text-sm flex items-center gap-2"> {/* Removed mb-1 */}
                                  <CheckCircle className="h-4 w-4" />
                                  {t('monthlyMeetings.decisionsTitle')} {/* Use translation key */}
                                </h4>
                                {renderContent(meeting.decisions_taken, `${meeting.id}-decisions`, expandedSections[`${meeting.id}-decisions`])}
                              </div>
                            )}

                            {/* Read More/Less buttons are now handled within renderContent for each section */}

                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                 <Card>
                  <CardContent className="p-8">
                    <div className="text-center py-12">
                      <p className="text-muted-foreground">{t('monthlyMeetings.noMeetings')}</p> {/* Use translation key */}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  );
}


// Loading component
function MonthlyMeetingsLoading() {
  return (
    <div className="container py-12">
      {/* Static content skeleton */}
       <Card className="mb-8">
        <CardContent className="p-8">
          <div className="space-y-4 text-center">
            <Skeleton className="h-8 w-3/4 mx-auto" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
        </CardContent>
      </Card>

      {/* Monthly Meetings Listing skeletons */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array(6).fill(null).map((_, index) => (
          <Card key={`skeleton-${index}`} className="overflow-hidden">
            <div className="relative aspect-video">
              <Skeleton className="absolute inset-0" />
            </div>
            <CardContent className="p-6 space-y-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}