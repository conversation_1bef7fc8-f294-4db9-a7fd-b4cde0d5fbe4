"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface ImageFormat {
  url: string
  width: number
  height: number
}

interface ImageData {
  id: number
  documentId: string
  name: string
  alternativeText: string | null
  formats: {
    large?: ImageFormat
    medium?: ImageFormat
    small?: ImageFormat
    thumbnail?: ImageFormat
  }
  url: string
  mime?: string
  previewUrl?: string
}

interface OurChamberData {
  id: number
  documentId: string
  title: string
  subtitle: string
  content: {
    type: string
    children: {
      text: string
      type: string
    }[]
  }[]
  order: string
  // Match the news page pattern - image can be an array or single object
  image?: ImageData[] | ImageData | { url: string } | null
  // Also support images property as fallback
  images?: ImageData[]
}

function OurChamberContent() {
  const searchParams = useSearchParams()
  const [chamberData, setChamberData] = useState<OurChamberData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set())

  // Get current language and translation function
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale from current language
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  // Toggle expanded state for read more functionality
  const toggleExpanded = (itemId: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  useEffect(() => {
    async function fetchData() {
      try {
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae'

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/our-chambers`)
        url.searchParams.append('populate', '*')
        if (getStrapiLocale) {
          url.searchParams.append('locale', getStrapiLocale)
        }

        console.log('Our Chamber - Current language:', currentLang)
        console.log('Our Chamber - Strapi locale:', getStrapiLocale)
        console.log('Our Chamber - API URL:', url.toString())

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        })

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        console.log('API Response:', data)

        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          setChamberData(data.data)
        } else {
          console.error('Chamber data not found in response:', data)
          setError('No chamber data found. Please check the API.')
        }
      } catch (error) {
        console.error('Error fetching our chamber data:', error)
        setError('Failed to load data. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Enhanced content rendering with read more functionality
  const renderContent = (content: any[], itemId: number) => {
    if (!content || !Array.isArray(content)) {
      return <p>{t('ourChamber.noContent')}</p>
    }

    // Convert content to text for length calculation
    const fullText = content
      .map(block => 
        block.children?.map((child: any) => child.text).join(' ')
      )
      .join(' ')

    const isExpanded = expandedItems.has(itemId)
    const maxLength = 300 // Characters to show before "Read More"
    const shouldTruncate = fullText.length > maxLength

    const displayContent = shouldTruncate && !isExpanded
      ? content.slice(0, 2) // Show first 2 paragraphs when truncated
      : content

    return (
      <div>
        <div className="space-y-4">
          {displayContent.map((block, index) => {
            if (block.type === "paragraph") {
              return (
                <p key={index} className="mb-4">
                  {block.children.map((child: any, childIndex: number) => (
                    <span key={childIndex}>{child.text}</span>
                  ))}
                </p>
              )
            }
            return null
          })}
        </div>
        {shouldTruncate && (
          <Button
            variant="link"
            onClick={() => toggleExpanded(itemId)}
            className="p-0 h-auto text-blue-600 hover:text-blue-800 mt-2"
          >
            {isExpanded ? t('ourChamber.readLess') : t('ourChamber.readMore')}
          </Button>
        )}
      </div>
    )
  }

  const getOptimalImageUrl = (image: ImageData) => {
    if (image.formats?.large?.url) return image.formats.large.url
    if (image.formats?.medium?.url) return image.formats.medium.url
    if (image.formats?.small?.url) return image.formats.small.url
    return image.url
  }

  const patchMediaMime = (mediaArr: ImageData[]): ImageData[] =>
    mediaArr.map((item: ImageData) =>
      item.mime
        ? item
        : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
    );

  // Local FullImageSlider for multiple images/videos
  function FullImageSlider({ images, alt, interval = 3000 }: {
    images: any[]
    alt: string
    interval?: number
  }) {
    const [currentIndex, setCurrentIndex] = useState(0)
    const videoRef = useRef<HTMLVideoElement | null>(null)

    // Handlers for arrows
    const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
    const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

    useEffect(() => {
      if (images.length <= 1) return

      const currentMedia = images[currentIndex]
      let timer: NodeJS.Timeout | null = null
      let videoEl: HTMLVideoElement | null = null

      if (currentMedia.mime?.startsWith('video/')) {
        videoEl = videoRef.current
        if (videoEl) {
          const handleEnded = () => {
            goToNext()
          }
          videoEl.addEventListener('ended', handleEnded)
          return () => {
            videoEl && videoEl.removeEventListener('ended', handleEnded)
          }
        }
      } else {
        timer = setInterval(() => {
          goToNext()
        }, interval)
        return () => {
          if (timer) clearInterval(timer)
        }
      }
    }, [images, currentIndex, interval])

    if (images.length === 0) {
      return (
        <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
          <p className="text-muted-foreground">No media available</p>
        </div>
      )
    }

    const currentMedia = images[currentIndex]

    return (
      <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
        {/* Left Arrow */}
        {images.length > 1 && (
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
            onClick={goToPrev}
            aria-label="Previous slide"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
        )}

        {/* Current Media */}
        <div className="relative w-full h-full flex items-center justify-center">
          {currentMedia.mime?.startsWith('video/') ? (
            <video
              ref={videoRef}
              src={currentMedia.url}
              poster={currentMedia.previewUrl}
              controls
              className="w-full h-full object-contain"
              playsInline
              autoPlay={false}
            />
          ) : (
            <Image
              src={currentMedia.url}
              alt={`${alt} - media ${currentIndex + 1}`}
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          )}
        </div>

        {/* Right Arrow */}
        {images.length > 1 && (
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
            onClick={goToNext}
            aria-label="Next slide"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        )}

        {/* Dots indicator for multiple media items */}
        {images.length > 1 && (
          <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
            <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
              {images.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentIndex ? "bg-white" : "bg-white/50"
                  } pointer-events-auto`}
                  onClick={() => setCurrentIndex(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="ourChamber.title"
        description="ourChamber.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('ourChamber.staticTitle')}</h2>
              <p>
                {t('ourChamber.staticDescription')}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardContent className="p-8">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                {error}
              </div>
            ) : chamberData.length > 0 ? (
              <div className="space-y-16">
                {chamberData.map((chamber, index) => (
                  <div key={chamber.id} className="prose prose-lg max-w-none">
                    {index > 0 && <hr className="my-8" />}
                    <div className="grid md:grid-cols-2 gap-8 items-start">
                      <div className="space-y-4">
                        <h1 className="text-3xl font-bold mb-2">{chamber.title}</h1>
                        <h2 className="text-xl text-muted-foreground mb-6">{chamber.subtitle}</h2>
                        {renderContent(chamber.content, chamber.id)}
                      </div>
                      <div className="relative aspect-video">
                        {/* Use the exact same pattern as news page */}
                        {Array.isArray(chamber.image) && chamber.image.length > 1 ? (
                          <FullImageSlider
                            images={patchMediaMime(chamber.image)}
                            alt={chamber.title || "Our Chamber"}
                            interval={4000}
                          />
                        ) : chamber.images && chamber.images.length > 1 ? (
                          <FullImageSlider
                            images={patchMediaMime(chamber.images)}
                            alt={chamber.title || "Our Chamber"}
                            interval={4000}
                          />
                        ) : Array.isArray(chamber.image) && chamber.image.length === 1 ? (
                          chamber.image[0].mime?.startsWith('video/') ? (
                            <video
                              src={chamber.image[0].url}
                              poster={chamber.image[0].previewUrl}
                              controls
                              className="w-full h-full object-contain rounded-lg"
                              playsInline
                            />
                          ) : (
                            <Image
                              src={getOptimalImageUrl(chamber.image[0])}
                              alt={chamber.title || "Our Chamber"}
                              fill
                              className="object-contain rounded-lg"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          )
                        ) : chamber.images && chamber.images.length === 1 ? (
                          chamber.images[0].mime?.startsWith('video/') ? (
                            <video
                              src={chamber.images[0].url}
                              poster={chamber.images[0].previewUrl}
                              controls
                              className="w-full h-full object-contain rounded-lg"
                              playsInline
                            />
                          ) : (
                            <Image
                              src={getOptimalImageUrl(chamber.images[0])}
                              alt={chamber.title || "Our Chamber"}
                              fill
                              className="object-contain rounded-lg"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          )
                        ) : (
                          <div className="relative aspect-[4/3]">
                            <Image
                              src={
                                typeof chamber.image === 'object' && !Array.isArray(chamber.image) && chamber.image?.url
                                  ? ('formats' in chamber.image ? getOptimalImageUrl(chamber.image as any) : chamber.image.url)
                                  : "/placeholder.svg"
                              }
                              alt={chamber.title || "Our Chamber"}
                              fill
                              className="object-contain rounded-lg"
                              onError={(e) => {
                                console.error('Error loading image');
                                (e.target as HTMLImageElement).src = "/placeholder.svg";
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('common.noData')}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Loading component for Suspense fallback
function OurChamberLoading() {
  return (
    <div className="min-h-screen">
      <div className="relative h-96 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="h-8 bg-white/20 rounded animate-pulse w-64 mx-auto mb-4" />
          <div className="h-4 bg-white/20 rounded animate-pulse w-96 mx-auto" />
        </div>
      </div>
      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4 mx-auto" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6 mx-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-8">
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-8 w-1/2 mt-8" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Main export with Suspense wrapper
export default function OurChamber() {
  return (
    <Suspense fallback={<OurChamberLoading />}>
      <OurChamberContent />
    </Suspense>
  )
}