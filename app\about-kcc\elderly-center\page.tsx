"use client"

import { useState, useEffect, useMemo, Suspense, useCallback } from "react"
import { useSearchParams } from 'next/navigation'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import { MapPin, Phone, Mail, Clock, ChevronDown, ChevronUp } from 'lucide-react'
import AnimatedHero from "@/components/animated-hero"
import EnhancedMediaSlider from '@/components/enhanced-media-slider'
import { useTranslation, getLanguageFromParams } from '@/lib/translations'
import { fetchElderlyCentersData, type ElderlyCenter } from '@/lib/strapi'

// Define media item interface for enhanced media slider
interface ElderlyCenterMediaItem {
  id: number
  url: string
  mime?: string
  previewUrl?: string | null
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: {
    large?: { url: string }
    medium?: { url: string }
    small?: { url: string }
    thumbnail?: { url: string }
  }
}

// Component that uses searchParams
function ElderlyCenterContent() {
  const [centersData, setCentersData] = useState<ElderlyCenter[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to get center media items compatible with EnhancedMediaSlider
  const getCenterMedia = useCallback((center: ElderlyCenter): ElderlyCenterMediaItem[] => {
    const mediaItems: ElderlyCenterMediaItem[] = [];
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

    if (center.image && Array.isArray(center.image)) {
      center.image.forEach((img) => {
        if (img && img.url) {
          mediaItems.push({
            id: img.id,
            url: img.url.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
            mime: img.mime || 'image/jpeg',
            previewUrl: img.previewUrl,
            alternativeText: img.alternativeText,
            caption: img.caption,
            width: img.width,
            height: img.height,
            formats: img.formats
          });
        }
      });
    }

    return mediaItems;
  }, [])

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('Fetching elderly center data for language:', currentLang);

        const centersData = await fetchElderlyCentersData({
          populate: '*',
          locale: getStrapiLocale
        });

        console.log('Fetched elderly center data:', centersData);

        if (isMounted) {
          if (centersData && centersData.length > 0) {
            setCentersData(centersData);
            setError(null);
          } else {
            setCentersData([]);
            setError(null); // Don't set error for empty data
          }
        }
      } catch (err: any) {
        if (isMounted) {
          console.error('Error fetching elderly center data:', err);
          setError('An error occurred while fetching elderly center data.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [currentLang, getStrapiLocale])

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: any[]) => {
    if (!content || !Array.isArray(content)) return false;

    const fullText = content
      .map(block => block.children?.map((child: any) => child.text).join('') || '')
      .join(' ');

    return fullText.length > 200;
  }

  // Content rendering with read more/less functionality
  const renderContent = useCallback((content: any[], centerId: number) => {
    if (!content || !Array.isArray(content)) {
      return <p>{t('elderlyCenter.noContent')}</p>
    }

    const isExpanded = expandedCards[centerId]
    const showReadMore = shouldShowReadMore(content)

    // Show only first 2 paragraphs when collapsed
    const displayContent = showReadMore && !isExpanded
      ? content.slice(0, 2)
      : content

    return (
      <div>
        <div className="space-y-4">
          {displayContent.map((block, index) => {
            if (block.type === "paragraph") {
              return (
                <p key={index} className="mb-4">
                  {block.children.map((child: any, childIndex: number) => (
                    <span key={childIndex}>{child.text}</span>
                  ))}
                </p>
              );
            }
            return null;
          })}
        </div>

        {showReadMore && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleExpanded(centerId)}
            className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
          >
            {isExpanded ? (
              <>
                {t('elderlyCenter.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
              </>
            ) : (
              <>
                {t('elderlyCenter.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
              </>
            )}
          </Button>
        )}
      </div>
    );
  }, [expandedCards, toggleExpanded, t]);

  const renderServices = useCallback((services: any[]) => {
    if (!services || !Array.isArray(services)) return <p>{t('elderlyCenter.noServices')}</p>;

    return (
      <ul className="list-disc pl-5 space-y-2">
        {services.map((item, index) => (
          <li key={index}>
            {item.type === "paragraph" ? item.children.map((child: any) => child.text).join("") : item}
          </li>
        ))}
      </ul>
    );
  }, [t]);

  // Render contact information
  const renderContactInfo = useCallback((contactInfo: ElderlyCenter['contactInfo']) => {
    if (!contactInfo) return null;

    return (
      <div className="mt-8 p-6 bg-gray-50 rounded-lg">
        <h3 className="text-xl font-semibold mb-4">{t('elderlyCenter.contactInfo')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {contactInfo.address && (
            <div className="flex items-start gap-3">
              <MapPin className="h-5 w-5 text-gray-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">{t('elderlyCenter.address')}</p>
                <p className="text-gray-600">{contactInfo.address}</p>
              </div>
            </div>
          )}
          {contactInfo.phone && (
            <div className="flex items-start gap-3">
              <Phone className="h-5 w-5 text-gray-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">{t('elderlyCenter.phone')}</p>
                <p className="text-gray-600">{contactInfo.phone}</p>
              </div>
            </div>
          )}
          {contactInfo.email && (
            <div className="flex items-start gap-3">
              <Mail className="h-5 w-5 text-gray-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">{t('elderlyCenter.email')}</p>
                <p className="text-gray-600">{contactInfo.email}</p>
              </div>
            </div>
          )}
          {contactInfo.hours && (
            <div className="flex items-start gap-3">
              <Clock className="h-5 w-5 text-gray-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">{t('elderlyCenter.hours')}</p>
                <p className="text-gray-600">{contactInfo.hours}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }, [t]);

  if (error) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title={{
            zh: t('elderlyCenter.title'),
            en: t('elderlyCenter.title'),
          }}
          description={{
            zh: t('elderlyCenter.description'),
            en: t('elderlyCenter.description'),
          }}
          image="/placeholder.svg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {error}
            </h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: t('elderlyCenter.title'),
          en: t('elderlyCenter.title'),
        }}
        description={{
          zh: t('elderlyCenter.description'),
          en: t('elderlyCenter.description'),
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('elderlyCenter.staticTitle')}</h2>
              <p>
                {t('elderlyCenter.staticDescription')}
              </p>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <Card className="overflow-hidden">
            <CardContent className="p-8">
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </CardContent>
          </Card>
        ) : centersData.length > 0 ? (
          <div className="space-y-12">
            {centersData.map((center) => {
              const mediaItems = getCenterMedia(center)
              const isExpanded = expandedCards[center.id]

              return (
                <Card key={center.id} className="overflow-hidden">
                  <CardContent className="p-8">
                    <div className="prose prose-lg max-w-none">
                      <div className="flex justify-between items-center mb-4">
                        <h1 className="text-3xl font-bold mb-0">{center.title}</h1>
                        {center.location && (
                          <div className="flex items-center gap-2 text-gray-600 bg-gray-100 px-3 py-1 rounded-full text-sm">
                            <MapPin className="h-4 w-4" />
                            <span>{center.location}</span>
                          </div>
                        )}
                      </div>

                      <div className="grid md:grid-cols-2 gap-8 items-start">
                        <div className="space-y-4">
                          {/* Description */}
                          {center.description && renderContent(center.description, center.id)}

                          {/* Services */}
                          {center.services && (
                            <div className="mt-6">
                              <h3 className="text-xl font-semibold mb-3">{t('elderlyCenter.servicesTitle')}</h3>
                              {renderServices(center.services)}
                            </div>
                          )}

                          {/* Read More/Less Button - Controls Contact Info Visibility */}
                          {center.contactInfo && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleExpanded(center.id)}
                              className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                            >
                              {isExpanded ? (
                                <>
                                  {t('elderlyCenter.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                </>
                              ) : (
                                <>
                                  {t('elderlyCenter.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                </>
                              )}
                            </Button>
                          )}
                        </div>

                        {/* Enhanced Media Slider for multiple images/videos */}
                        {mediaItems.length > 0 ? (
                          <div className="mt-6 md:mt-0">
                            <EnhancedMediaSlider
                              media={mediaItems}
                              alt={center.title || t('elderlyCenter.defaultMediaAlt')}
                              interval={3000}
                            />
                          </div>
                        ) : (
                          <div className="relative aspect-video mt-6 md:mt-0">
                            <Image
                              src="/placeholder.svg"
                              alt={center.title || t('elderlyCenter.defaultMediaAlt')}
                              fill
                              className="object-cover rounded-lg"
                            />
                          </div>
                        )}
                      </div>

                      {/* Contact Information Section - Only show when expanded */}
                      {isExpanded && renderContactInfo(center.contactInfo)}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        ) : (
          <Card className="overflow-hidden">
            <CardContent className="p-8">
              <div className="text-center py-12 text-muted-foreground">
                {t('elderlyCenter.noData')}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

// Main component with Suspense wrapper
export default function ElderlyCenter() {
  return (
    <Suspense fallback={
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: "耆英中心",
            en: "Elderly Center",
          }}
          description={{
            zh: "了解我們的耆英中心服務和設施",
            en: "Learn about our elderly center services and facilities",
          }}
          image="/placeholder.svg"
        />
        <div className="container py-12">
          <Card className="overflow-hidden">
            <CardContent className="p-8">
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    }>
      <ElderlyCenterContent />
    </Suspense>
  )
}