"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"

interface KccInterviewData {
  id: number;
  documentId: string;
  title: string;
  interviewee?: string;
  date?: string;
  content?: {
    type: string;
    children: {
      text: string;
      type: string;
    }[];
  }[];
  order?: string;
  image?: {
    url: string;
    formats?: {
      medium?: { url: string };
      small?: { url: string };
      large?: { url: string };
    };
  };
}

export default function KccInterviews() {
  const [interviewData, setInterviewData] = useState<KccInterviewData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    async function fetchData() {
      try {
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae';

        const response = await fetch(`${strapiUrl}/cc-interviews?populate=*`, {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const interviews = data?.data;

        if (interviews && Array.isArray(interviews) && interviews.length > 0) {
          // Map all interviews data
          const allInterviews = interviews.map((interview: any) => ({
            id: interview.id,
            documentId: interview.documentId,
            title: interview.title,
            interviewee: interview.interviewee,
            date: interview.date,
            content: interview.content,
            order: interview.order,
            image: interview.image || null,
          }));
          setInterviewData(allInterviews);
          setDebugInfo(`Found ${allInterviews.length} interviews.`);
        } else {
          setError("No interview data found.");
        }
      } catch (error: any) {
        setError(`Failed to load data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const renderContent = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p>No content available</p>;
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null;
    });
  };

  const getImageUrl = (interview: KccInterviewData) => {
    const imageFormats = interview?.image?.formats;
    return imageFormats?.medium?.url || interview?.image?.url || "/placeholder.svg";
  };

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "龍總媒體專訪",
          en: "Interviews on KCC",
        }}
        description={{
          zh: "關於九龍總商會的媒體專訪",
          en: "Media interviews about KCC",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">龍總媒體專訪 / Interviews on KCC</h2>
              <p>
                九龍總商會定期接受各大媒體的專訪，分享商會的發展、成就和未來展望。
                這些專訪展示了商會在促進香港商業發展和維護會員權益方面的重要角色，
                也反映了商會對社會經濟變化的活躍應變。
              </p>
              <p>
                The Kowloon Chamber of Commerce regularly accepts interviews from major media outlets, sharing the Chamber's development, achievements, and future prospects.
                These interviews showcase the Chamber's important role in promoting Hong Kong's business development and protecting members' interests,
                and also reflect the Chamber's active response to socio-economic changes.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardContent className="p-8">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            ) : interviewData.length > 0 ? (
              <div className="space-y-16">
                {interviewData.map((interview, index) => (
                  <div key={interview.id} className="prose prose-lg max-w-none">
                    {index > 0 && <hr className="my-8" />}
                    <h1 className="text-3xl font-bold mb-2">{interview.title}</h1>

                    <div className="grid md:grid-cols-2 gap-8 items-start">
                      <div className="space-y-4">
                        {interview.interviewee && (
                          <p className="text-lg text-muted-foreground">
                            <strong>Interviewee:</strong> {interview.interviewee}
                          </p>
                        )}

                        {interview.date && (
                          <p className="text-lg text-muted-foreground">
                            <strong>Date:</strong> {interview.date}
                          </p>
                        )}

                        {interview.content && renderContent(interview.content)}
                      </div>

                      <div className="relative aspect-video mt-6 md:mt-0">
                        <Image
                          src={getImageUrl(interview)}
                          alt={`Interview with ${interview.interviewee || 'KCC'}`}
                          fill
                          className="object-cover rounded-lg"
                          sizes="(max-width: 768px) 100vw, 50vw"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
