"use client"

import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { motion } from "framer-motion"

interface BreadcrumbProps {
  items: {
    title: {
      en: string
      zh: string
    }
    path: string
  }[]
}

export default function Breadcrumb({ items }: BreadcrumbProps) {
  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground py-2">
      {items.map((item, index) => (
        <motion.div
          key={index}
          className="flex items-center"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          {index > 0 && <ChevronRight className="h-4 w-4 mx-2" />}
          <Link
            href={item.path}
            className={`hover:text-primary transition-colors ${
              index === items.length - 1 ? "text-foreground font-medium" : "text-muted-foreground"
            }`}
          >
            <span className="inline-block">
              {item.title.en}
              <span className="text-muted-foreground/60 mx-1">/</span>
              {item.title.zh}
            </span>
          </Link>
        </motion.div>
      ))}
    </nav>
  )
}

