import Image from "next/image"
import { ChevronRight } from "lucide-react"

export default function ActivitiesSection() {
  return (
    <section>
      <h2 className="text-xl font-bold mb-6 flex items-center">
        活動重溫
        <ChevronRight className="h-5 w-5 text-[#1E1B4B]" />
      </h2>
      <div className="grid grid-cols-3 gap-4">
        {[...Array(3)].map((_, i) => (
          <div key={i}>
            <div className="relative aspect-video mb-2">
              <Image src="/placeholder.svg" alt={`Activity ${i + 1}`} fill className="object-cover rounded" />
            </div>
            <h3 className="text-sm font-medium">活動標題 {i + 1}</h3>
          </div>
        ))}
      </div>
    </section>
  )
}

