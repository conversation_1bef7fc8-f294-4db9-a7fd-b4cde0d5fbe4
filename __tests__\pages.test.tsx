import type React from "react"
import { render, screen } from "@testing-library/react"
import { describe, expect, it, vi } from "vitest"
import { routes } from "@/app/routes"

// Import all pages
import HomePage from "@/app/page"
import AboutPage from "@/app/about/page"
import HistoryPage from "@/app/history/page"
import EventsPage from "@/app/events/page"
import ActivitiesPage from "@/app/activities/page"
import NewsPage from "@/app/news/page"
import ContactPage from "@/app/contact/page"
import ChineseMedicinePage from "@/app/resources/chinese-medicine/page"
import ChairmanSpeechesPage from "@/app/about/chairman-speeches/page"
import HistoricalBoardMembersPage from "@/app/about/board-members/historical/page"

// Mock next/image
vi.mock("next/image", () => ({
  default: (props: any) => <img {...props} />,
}))

// Mock next/link
vi.mock("next/link", () => ({
  default: ({ children, href }: { children: React.ReactNode; href: string }) => <a href={href}>{children}</a>,
}))

describe("Page Structure Tests", () => {
  // Test if all required pages exist
  it("should have all required pages based on routes.ts", () => {
    // Core pages
    expect(HomePage).toBeDefined()
    expect(AboutPage).toBeDefined()
    expect(HistoryPage).toBeDefined()
    expect(EventsPage).toBeDefined()
    expect(ActivitiesPage).toBeDefined()
    expect(NewsPage).toBeDefined()
    expect(ContactPage).toBeDefined()

    // Resource pages
    expect(ChineseMedicinePage).toBeDefined()

    // About section pages
    expect(ChairmanSpeechesPage).toBeDefined()
    expect(HistoricalBoardMembersPage).toBeDefined()
  })

  // Test History page content
  it("should render History page with timeline", () => {
    render(<HistoryPage />)

    // Check for bilingual titles
    expect(screen.getByText("歷史")).toBeInTheDocument()
    expect(screen.getByText("History")).toBeInTheDocument()

    // Check for key historical events
    expect(screen.getByText("1938")).toBeInTheDocument()
    expect(screen.getByText("1945")).toBeInTheDocument()
    expect(screen.getByText("1950")).toBeInTheDocument()

    // Check for timeline structure
    expect(screen.getByText("發展里程")).toBeInTheDocument()
    expect(screen.getByText("Milestones")).toBeInTheDocument()
  })

  // Test Chinese Medicine page content
  it("should render Chinese Medicine page with all sections", () => {
    render(<ChineseMedicinePage />)

    // Check for bilingual titles
    expect(screen.getByText("中醫中藥發展資訊")).toBeInTheDocument()
    expect(screen.getByText("Chinese Medicine Development")).toBeInTheDocument()

    // Check for tab sections
    expect(screen.getByText("出版刊物")).toBeInTheDocument()
    expect(screen.getByText("相關新聞")).toBeInTheDocument()
    expect(screen.getByText("活動資訊")).toBeInTheDocument()

    // Check for breadcrumb
    const breadcrumbLinks = screen.getAllByRole("link")
    expect(breadcrumbLinks.some((link) => link.getAttribute("href") === "/")).toBeTruthy()
    expect(breadcrumbLinks.some((link) => link.getAttribute("href") === "/resources")).toBeTruthy()
  })

  // Test navigation structure
  it("should have correct navigation links in header", () => {
    const { mainNavigation } = require("@/app/routes")

    // Check if all main navigation routes are defined
    mainNavigation.forEach((item: any) => {
      expect(routes).toHaveProperty(item.path.split("/")[1])
    })
  })

  // Test bilingual support
  it("should have bilingual content in all pages", () => {
    const pages = [
      { component: HistoryPage, zh: "歷史", en: "History" },
      { component: ChineseMedicinePage, zh: "中醫中藥發展資訊", en: "Chinese Medicine Development" },
      { component: ChairmanSpeechesPage, zh: "會長演講辭", en: "Chairman's Speeches" },
      { component: HistoricalBoardMembersPage, zh: "歷屆理監事名表", en: "Historical Board Members" },
    ]

    pages.forEach(({ component: Component, zh, en }) => {
      render(<Component />)
      expect(screen.getByText(zh)).toBeInTheDocument()
      expect(screen.getByText(en)).toBeInTheDocument()
    })
  })

  // Test responsive design
  it("should have responsive layout classes", () => {
    const pages = [HistoryPage, ChineseMedicinePage]

    pages.forEach((Component) => {
      const { container } = render(<Component />)

      // Check for responsive grid classes
      expect(container.innerHTML.includes("md:grid-cols-")).toBeTruthy()

      // Check for responsive padding/margin classes
      expect(container.innerHTML.includes("md:p-")).toBeTruthy()
    })
  })
})

