'use client'

import { useState, useEffect, useMemo, useCallback, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { useTranslation, getLanguageFromParams, type TranslationKey } from '@/lib/translations'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ImageIcon, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchOtherActivities, type OtherActivity } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import EnhancedMediaSlider from '@/components/enhanced-media-slider'

// Create a wrapper component that uses useSearchParams
function OtherActivitiesContent() {
  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const [activities, setActivities] = useState<OtherActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 100;
  }

  // Add locale tracking
  const strapiLocale = useMemo(() => {
    const locale = currentLang === 'zh' ? 'zh-Hant-HK' : currentLang === 'cn' ? 'zh-Hans-HK' : 'en'
    console.log('Using Strapi locale:', locale)
    return locale
  }, [currentLang])

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Starting fetch with locale:', strapiLocale)
        const activitiesData = await fetchOtherActivities({ 
          populate: '*',
          locale: strapiLocale
        })
        console.log('API Response:', activitiesData)

        if (activitiesData && activitiesData.length > 0) {
          console.log('Setting activities:', activitiesData.length)
          setActivities(activitiesData)
        } else {
          console.log('No activities found')
          setError(t('otherActivities.noActivities' as TranslationKey))
        }
      } catch (err) {
        console.error('API Error Details:', {
          error: err,
          locale: strapiLocale,
          message: err instanceof Error ? err.message : 'Unknown error'
        })
        setError(t('common.error' as TranslationKey))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [strapiLocale, t])

  if (error) {
    return (
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            en: t('otherActivities.title' as TranslationKey),
            zh: t('otherActivities.title' as TranslationKey),
            cn: t('otherActivities.title' as TranslationKey)
          }}
          description={{
            en: t('otherActivities.description' as TranslationKey),
            zh: t('otherActivities.description' as TranslationKey),
            cn: t('otherActivities.description' as TranslationKey)
          }}
          image="/placeholder.svg"
          lang={currentLang}
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          en: t('otherActivities.title' as TranslationKey),
          zh: t('otherActivities.title' as TranslationKey),
          cn: t('otherActivities.title' as TranslationKey)
        }}
        description={{
          en: t('otherActivities.description' as TranslationKey),
          zh: t('otherActivities.description' as TranslationKey),
          cn: t('otherActivities.description' as TranslationKey)
        }}
        image="/placeholder.svg"
        lang={currentLang}
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('otherActivities.staticTitle' as TranslationKey)}</h2>
              <p>{t('otherActivities.staticDescription' as TranslationKey)}</p>
            </div>
          </CardContent>
        </Card>

        {/* Other Activities Listing */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Other Activities Items */}
              {activities.length > 0 ? (
                <div className="space-y-6">

                  <div className="grid md:grid-cols-2 gap-6">
                    {activities.map((activity) => {
                      const isExpanded = expandedCards[activity.id]
                      const showReadMore = shouldShowReadMore(activity.description)

                      return (
                        <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {/* Image Slider for multiple images */}
                          {activity.image && activity.image.length > 0 ? (
                            <EnhancedMediaSlider
                              media={activity.image.map(img => ({
                                id: img.id,
                                url: img.url,
                                formats: img.formats,
                                mime: img.mime || 'image/jpeg', // Add mime type
                                caption: img.caption || '',
                                alternativeText: img.alternativeText || ''
                              }))}
                              alt={activity.event_name || t('otherActivities.defaultMediaAlt' as TranslationKey)}
                              interval={5000}
                              key={`slider-${activity.id}`}
                            />
                          ) : (
                            <div className="relative aspect-video">
                              <Image
                                src="/placeholder.svg"
                                alt={activity.event_name || "Other Activity"}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{activity.event_name}</h3>
                            <div className="space-y-2 mb-4">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{activity.location}</span>
                              </div>
                              {activity.organizer && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{activity.organizer}</span>
                                </div>
                              )}
                            </div>
                            <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                              {activity.description}
                            </div>

                            {showReadMore && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(activity.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    {t('otherActivities.readLess' as TranslationKey)} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('common.readMore' as TranslationKey)} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    {t('otherActivities.noActivities' as TranslationKey)}
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

// Main component with Suspense boundary
export default function OtherActivitiesPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen">
        <div className="container py-12">
          <div className="grid md:grid-cols-2 gap-6">
            {Array(4).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  <Skeleton className="absolute inset-0" />
                </div>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    }>
      <OtherActivitiesContent />
    </Suspense>
  )
}
