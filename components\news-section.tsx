import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"

export default function NewsSection() {
  const news = [
    "中總舉辦迎春花市展銷會暨新春商貿推廣活動",
    "中港通關措施完善建議書出爐",
    "中總舉辦「125周年會慶-愛心敬老粵劇慈善晚會」",
    "年度立法會商議分享交流工作會議",
    "香港中華總商會第72屆會董就職",
  ]

  return (
    <div className="relative">
      <div className="absolute left-0 top-0 bottom-0 w-1 bg-[#1E1B4B]" />
      <div className="pl-6">
        <h2 className="text-xl font-bold mb-6 flex items-center">
          新聞稿
          <ChevronRight className="h-5 w-5 text-[#1E1B4B]" />
        </h2>
        <div className="space-y-4">
          {news.map((title, i) => (
            <Link href="#" key={i} className="flex items-start gap-4 group">
              <div className="w-2 h-2 mt-2 rounded-full bg-[#1E1B4B]" />
              <div>
                <span className="text-gray-500 text-sm block">2024-02-{21 - i}</span>
                <h3 className="group-hover:text-[#1E1B4B]">{title}</h3>
              </div>
            </Link>
          ))}
        </div>
        <Button variant="outline" className="mt-6">
          更多 →
        </Button>
      </div>
    </div>
  )
}

