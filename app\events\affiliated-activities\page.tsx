'use client'

import { useState, useEffect, useMemo, Suspense } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Building, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import AnimatedHero from '@/components/animated-hero'
// Import necessary types and fetch function
import { fetchExpoActivitiesData, type ExpoActivity } from '@/lib/strapi'
// import ImageSlider from '@/components/image-slider' // Remove ImageSlider import
import EnhancedMediaSlider from '@/components/enhanced-media-slider' // Import EnhancedMediaSlider
import { useTranslation, getLanguageFromParams, type TranslationKey } from '@/lib/translations' // Import TranslationKey
import { useSearchParams } from 'next/navigation'

// Component for handling expandable text
function ExpandableText({ text, maxLength = 150, t }: { text: string; maxLength?: number; t: (key: TranslationKey) => string }) { // Update t prop type
  const [isExpanded, setIsExpanded] = useState(false)

  if (text.length <= maxLength) {
    return <div className="text-muted-foreground">{text}</div>
  }

  return (
    <div className="text-muted-foreground">
      <div>
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
      >
        {isExpanded ? (
          <>
            <ChevronUp className="h-4 w-4 mr-1" />
            {t('affiliatedActivities.readLess' as TranslationKey)} {/* Cast key */}
          </>
        ) : (
          <>
            <ChevronDown className="h-4 w-4 mr-1" />
            {t('common.readMore' as TranslationKey)} {/* Cast key */}
          </>
        )}
      </Button>
    </div>
  )
}

// Main component with Suspense wrapper
export default function AffiliatedActivitiesPage() {
  return (
    <Suspense fallback={<AffiliatedActivitiesLoading />}>
      <AffiliatedActivitiesContent />
    </Suspense>
  )
}

function AffiliatedActivitiesContent() {
  const [activities, setActivities] = useState<ExpoActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  useEffect(() => {
    let isMounted = true

    const loadExpoActivities = async () => {
      try {
        setLoading(true)
        setError(null)
        console.log('Fetching expo activities data for language:', currentLang)

        const expoActivitiesData = await fetchExpoActivitiesData({
          populate: "*",
          locale: getStrapiLocale
        })

        if (!isMounted) return

        if (expoActivitiesData && expoActivitiesData.length > 0) {
          // Filter out duplicate entries based on documentId
          const uniqueActivities = Array.from(new Map(expoActivitiesData.map(item => [item.documentId, item])).values());

          // Sort unique activities by date (most recent first)
          const sortedActivities = [...uniqueActivities].sort((a, b) => {
            if (!a.date && !b.date) return 0
            if (!a.date) return 1
            if (!b.date) return -1
            return new Date(b.date).getTime() - new Date(a.date).getTime()
          })
          setActivities(sortedActivities)
        } else {
          // Fallback without locale
          try {
            const fallbackResponse = await fetchExpoActivitiesData({ populate: '*' })

            if (!isMounted) return

            if (fallbackResponse && fallbackResponse.length > 0) {
               // Filter out duplicate entries based on documentId for fallback
              const uniqueFallbackActivities = Array.from(new Map(fallbackResponse.map(item => [item.documentId, item])).values());

              const sortedFallback = [...uniqueFallbackActivities].sort((a, b) => {
                if (!a.date && !b.date) return 0
                if (!a.date) return 1
                if (!b.date) return -1
                return new Date(b.date).getTime() - new Date(a.date).getTime()
              })
              setActivities(sortedFallback)
            } else {
              setError(t('affiliatedActivities.noActivities' as TranslationKey)) // Cast key
            }
          } catch (fallbackErr) {
            if (isMounted) {
              setError(t('affiliatedActivities.noActivities' as TranslationKey)) // Cast key
            }
          }
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching expo activities:', err)
          setError(t('affiliatedActivities.error' as TranslationKey)) // Cast key
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    loadExpoActivities()

    return () => {
      isMounted = false
    }
  }, [currentLang, getStrapiLocale])

  // Define the title and description objects for AnimatedHero with translations for each language
  const heroTitle = useMemo(() => ({
    en: t('affiliatedActivities.staticTitle' as TranslationKey) as string,
    zh: t('affiliatedActivities.staticTitle' as TranslationKey) as string,
    cn: t('affiliatedActivities.staticTitle' as TranslationKey) as string,
  }), [t]);

  const heroDescription = useMemo(() => ({
    en: t('affiliatedActivities.staticDescription' as TranslationKey) as string,
    zh: t('affiliatedActivities.staticDescription' as TranslationKey) as string,
    cn: t('affiliatedActivities.staticDescription' as TranslationKey) as string,
  }), [t]);


  return (
    <div className={`min-h-screen bg-gradient-to-br from="blue-50" to="indigo-100"`}> {/* Using template literal */}
      <AnimatedHero
        title={heroTitle} // Use the memoized heroTitle object
        description={heroDescription} // Use the memoized heroDescription object
        image="/placeholder.svg"
        lang={currentLang}
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold text-center mb-4">
              {t('affiliatedActivities.activitiesTitle' as TranslationKey)} {/* Cast key */}
            </h2>
            <p className="text-center text-muted-foreground">
              {t('affiliatedActivities.activitiesDescription' as TranslationKey)} {/* Cast key */}
            </p>
          </CardContent>
        </Card>

        {loading && <AffiliatedActivitiesLoading />}

        {error && (
          <div className="text-center text-red-500 my-8">
            <p>{error}</p>
          </div>
        )}

        {!loading && !error && activities.length === 0 && (
          <div className="text-center text-muted-foreground my-8">
            <p>{t('affiliatedActivities.noContent' as TranslationKey)} {/* Cast key */}</p>
          </div>
        )}

        {!loading && !error && activities.length > 0 && (
          <div className="grid md:grid-cols-2 gap-6">
            {activities.map((activity, index) => (
              <Card key={`activity-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  {/* Use activity.image and check if it's an array for EnhancedMediaSlider */}
                  {activity.image && (Array.isArray(activity.image) ? activity.image.length > 0 : true) ? (
                     <EnhancedMediaSlider
                        media={Array.isArray(activity.image) ? activity.image : [activity.image]}
                       alt={(activity as any).expo_name || t('affiliatedActivities.defaultMediaAlt' as TranslationKey)} // Use expo_name and cast key
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-gray-100">
                      <p className="text-muted-foreground">
                        {t('affiliatedActivities.noMedia' as TranslationKey)} {/* Cast key */}
                      </p>
                    </div>
                  )}
                </div>
                <CardContent className="p-6">
                  {/* Use activity.expo_name for the title */}
                  <h3 className="text-xl font-semibold mb-2">{activity.expo_name}</h3> {/* Use expo_name */}
                  {activity.date && (
                    <div className="flex items-center text-muted-foreground mb-2">
                      <Calendar className="h-4 w-4 mr-2" />
                      <time dateTime={activity.date}>
                        {new Date(activity.date).toLocaleDateString(
                          currentLang === 'en' ? 'en-US' : 'zh-HK',
                          { year: 'numeric', month: 'long', day: 'numeric' }
                        )}
                      </time>
                    </div>
                  )}
                  {activity.location && (
                    <div className="flex items-center text-muted-foreground mb-4">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{activity.location}</span>
                    </div>
                  )}
                  {/* Ensure description is a string for ExpandableText */}
                  {activity.description && (
                    <ExpandableText
                      text={String(activity.description)} // Ensure description is string
                      maxLength={150}
                      t={t}
                    />
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Loading component
function AffiliatedActivitiesLoading() {
   // Get translation function for loading component
   const { t } = useTranslation(); // Use useTranslation hook
  return (
    <div className={`min-h-screen bg-gradient-to-br from="blue-50" to="indigo-100"`}> {/* Using template literal */}
      <div className="w-full aspect-video bg-gray-200 animate-pulse"></div>
      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="space-y-4 text-center">
              <Skeleton className="h-8 w-3/4 mx-auto" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>

        <div className="grid md:grid-cols-2 gap-6">
          {Array(4).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <div className="relative aspect-video">
                <Skeleton className="absolute inset-0" />
              </div>
              <CardContent className="p-6 space-y-4">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}