'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, Users, Award } from 'lucide-react'
import AnimatedHero from '@/components/animated-hero'

// Mock data for past councils - replace with actual Strapi data when available
const mockPastCouncils = [
  {
    id: 1,
    term: "第四十五屆 (2023-2025)",
    termEn: "45th Term (2023-2025)",
    chairman: "陳經緯",
    chairmanEn: "<PERSON>",
    viceChairmen: ["李家傑", "胡曉明"],
    viceChairmenEn: ["<PERSON> Ka <PERSON>", "<PERSON> Hiu <PERSON>"],
    directors: ["張學友", "劉德華", "梁朝偉", "周潤發"],
    directorsEn: ["<PERSON>", "<PERSON> Tak Wah", "<PERSON><PERSON>", "<PERSON> Yun Fat"],
    year: "2023"
  },
  {
    id: 2,
    term: "第四十四屆 (2021-2023)",
    termEn: "44th Term (2021-2023)",
    chairman: "胡曉明",
    chairmanEn: "<PERSON>",
    viceChairmen: ["陳經緯", "李家傑"],
    viceChairmenEn: ["<PERSON> King <PERSON><PERSON>", "<PERSON>"],
    directors: ["黃秋生", "吳鎮宇", "古天樂", "劉青雲"],
    directorsEn: ["<PERSON> <PERSON>u <PERSON>", "Ng Chin Yu", "Koo Tin <PERSON>", "Lau Ching Wan"],
    year: "2021"
  },
  {
    id: 3,
    term: "第四十三屆 (2019-2021)",
    termEn: "43rd Term (2019-2021)",
    chairman: "李家傑",
    chairmanEn: "Lee Ka Kit",
    viceChairmen: ["陳經緯", "胡曉明"],
    viceChairmenEn: ["Chan King Wai", "Wu Hiu Ming"],
    directors: ["梁家輝", "鄭伊健", "陳小春", "謝霆鋒"],
    directorsEn: ["Leung Ka Fai", "Cheng Yee Kin", "Chan Siu Chun", "Tse Ting Fung"],
    year: "2019"
  }
]

export default function PastCouncilsPage() {
  const [councils, setCouncils] = useState(mockPastCouncils)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (error) {
    return (
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: "歷屆理監事明表",
            en: "Past Council Lists",
          }}
          description={{
            zh: "九龍總商會歷屆理監事會成員名單及任期記錄",
            en: "Historical records of KCC Board of Directors and Supervisors by term",
          }}
          image="/placeholder.svg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "歷屆理監事明表",
          en: "Past Council Lists",
        }}
        description={{
          zh: "九龍總商會歷屆理監事會成員名單及任期記錄",
          en: "Historical records of KCC Board of Directors and Supervisors by term",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">歷屆理監事明表 / Past Council Lists</h2>
              <p>
                九龍總商會自成立以來，歷屆理監事會成員都是來自各行各業的傑出領袖，
                他們以豐富的經驗和卓越的領導能力，帶領商會不斷發展壯大，為香港經濟發展作出重要貢獻。
              </p>
              <p>
                Since its establishment, the KCC Board of Directors and Supervisors have been outstanding leaders from various industries,
                who have led the Chamber's continuous growth and development with their rich experience and excellent leadership,
                making important contributions to Hong Kong's economic development.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Past Councils Listing */}
        <section>
          {loading ? (
            <div className="space-y-6">
              {Array(3).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-32" />
                    <div className="grid md:grid-cols-3 gap-4">
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-20 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {councils.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">歷屆理監事會</span> / Past Councils
                    </h2>
                    <p className="text-muted-foreground">九龍總商會歷屆理監事會成員名單 / Historical KCC Board members by term</p>
                  </div>

                  <div className="space-y-6">
                    {councils.map((council) => (
                      <Card key={council.id} className="overflow-hidden hover:shadow-lg transition-all">
                        <CardContent className="p-6">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                            <Calendar className="h-4 w-4" />
                            <span>{council.term} / {council.termEn}</span>
                          </div>
                          
                          <h3 className="text-xl font-bold mb-6">{council.term}</h3>
                          
                          <div className="grid md:grid-cols-3 gap-6">
                            {/* Chairman */}
                            <div className="space-y-3">
                              <h4 className="font-semibold text-lg flex items-center gap-2">
                                <Award className="h-5 w-5 text-yellow-600" />
                                會長 / Chairman
                              </h4>
                              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                <div className="font-medium text-lg">{council.chairman}</div>
                                <div className="text-sm text-muted-foreground">{council.chairmanEn}</div>
                              </div>
                            </div>

                            {/* Vice Chairmen */}
                            <div className="space-y-3">
                              <h4 className="font-semibold text-lg flex items-center gap-2">
                                <Users className="h-5 w-5 text-blue-600" />
                                副會長 / Vice Chairmen
                              </h4>
                              <div className="space-y-2">
                                {council.viceChairmen.map((viceChairman, index) => (
                                  <div key={index} className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                                    <div className="font-medium">{viceChairman}</div>
                                    <div className="text-sm text-muted-foreground">{council.viceChairmenEn[index]}</div>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* Directors */}
                            <div className="space-y-3">
                              <h4 className="font-semibold text-lg flex items-center gap-2">
                                <Users className="h-5 w-5 text-green-600" />
                                理事 / Directors
                              </h4>
                              <div className="space-y-2 max-h-48 overflow-y-auto">
                                {council.directors.map((director, index) => (
                                  <div key={index} className="bg-green-50 p-2 rounded border border-green-200">
                                    <div className="font-medium text-sm">{director}</div>
                                    <div className="text-xs text-muted-foreground">{council.directorsEn[index]}</div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    <span className="block">暫無歷屆理監事資料</span>
                    <span className="block">No past council records available at the moment</span>
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}
