import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Building2, Globe2, Calendar, ChevronRight } from "lucide-react"
import Link from "next/link"

export default function BusinessPage() {
  const opportunities = [
    {
      id: 1,
      title: "本地商業合作",
      description: "連接本地企業，拓展商業網絡，尋找合作夥伴",
      icon: <Building2 className="h-8 w-8 text-[#1E1B4B]" />,
      link: "/business/local",
    },
    {
      id: 2,
      title: "國際貿易機會",
      description: "探索國際市場，了解最新貿易政策和機遇",
      icon: <Globe2 className="h-8 w-8 text-[#1E1B4B]" />,
      link: "/business/international",
    },
    {
      id: 3,
      title: "展覽與會議",
      description: "參與各類商業展覽和會議，展示產品和服務",
      icon: <Calendar className="h-8 w-8 text-[#1E1B4B]" />,
      link: "/business/exhibitions",
    },
  ]

  const partners = [
    {
      id: 1,
      name: "香港貿易發展局",
      logo: "/placeholder.svg",
      link: "https://www.hktdc.com/",
    },
    {
      id: 2,
      name: "香港生產力促進局",
      logo: "/placeholder.svg",
      link: "https://www.hkpc.org/",
    },
    {
      id: 3,
      name: "香港中華總商會",
      logo: "/placeholder.svg",
      link: "https://www.cgcc.org.hk/",
    },
    {
      id: 4,
      name: "香港工業總會",
      logo: "/placeholder.svg",
      link: "https://www.industryhk.org/",
    },
    {
      id: 5,
      name: "香港總商會",
      logo: "/placeholder.svg",
      link: "https://www.chamber.org.hk/",
    },
    {
      id: 6,
      name: "中國國際貿易促進委員會",
      logo: "/placeholder.svg",
      link: "https://www.ccpit.org/",
    },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[40vh] flex items-center justify-center bg-[#1E1B4B]">
        <div className="absolute inset-0">
          <Image src="/placeholder.svg" alt="Business" fill className="object-cover opacity-20" />
        </div>
        <div className="relative text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">商務機會 / Business Opportunities</h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto px-4">
            九龍總商會為會員提供多元化商業機會，促進商貿合作與發展
          </p>
        </div>
      </section>

      <div className="container py-12">
        {/* Business Opportunities */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold mb-8 flex items-center">
            商業機會
            <ChevronRight className="ml-2 h-5 w-5 text-[#1E1B4B]" />
          </h2>

          <div className="grid md:grid-cols-3 gap-8">
            {opportunities.map((opp) => (
              <Card key={opp.id} className="hover:shadow-lg transition-all">
                <CardContent className="p-8 text-center">
                  <div className="flex justify-center mb-6">{opp.icon}</div>
                  <h3 className="text-xl font-bold mb-4">{opp.title}</h3>
                  <p className="text-gray-600 mb-6">{opp.description}</p>
                  <Button className="bg-[#1E1B4B] hover:bg-[#1E1B4B]/90" asChild>
                    <Link href={opp.link}>
                      了解更多 <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Partner Organizations */}
        <section>
          <h2 className="text-2xl font-bold mb-8 flex items-center">
            合作夥伴
            <ChevronRight className="ml-2 h-5 w-5 text-[#1E1B4B]" />
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {partners.map((partner) => (
              <Link key={partner.id} href={partner.link} target="_blank" rel="noopener noreferrer" className="group">
                <Card className="hover:shadow-md transition-all h-full">
                  <CardContent className="p-4 flex flex-col items-center justify-center h-full">
                    <div className="relative w-full aspect-square mb-4">
                      <Image
                        src={partner.logo || "/placeholder.svg"}
                        alt={partner.name}
                        fill
                        className="object-contain p-2"
                      />
                    </div>
                    <p className="text-sm text-center group-hover:text-[#1E1B4B]">{partner.name}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}

