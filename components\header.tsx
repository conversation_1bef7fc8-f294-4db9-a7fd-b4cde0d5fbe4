"use client"

import { useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, ChevronDown, LogOut } from "lucide-react"
import { useUser } from "@/contexts/user-context"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import { mainNavigation } from "@/app/routes"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import LanguageLink from "@/components/language-link"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const { user, logout } = useUser()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Get current language and translation function
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  // Helper function to get localized title from navigation items
  const getLocalizedTitle = (titleObj: { zh: string; en: string }) => {
    if (currentLang === 'zh' || currentLang === 'cn') {
      return titleObj.zh
    }
    return titleObj.en
  }

  const toggleExpanded = (path: string) => {
    setExpandedItems(prev =>
      prev.includes(path)
        ? prev.filter(p => p !== path)
        : [...prev, path]
    )
  }

  // Helper function to create language links that preserve current path
  const createLanguageLink = (lang: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('lang', lang)
    return `${pathname}?${params.toString()}`
  }

  return (
    <header>
      {/* Top Bar */}
      <div className="bg-[#1E1B4B] text-white">
        <div className="container">
          <div className="flex justify-end h-8 items-center text-sm">
            {user ? (
              <div className="flex items-center gap-4">
                <LanguageLink href="/dashboard" className="hover:underline">
                  {t('header.welcome')}, {user.username}
                </LanguageLink>
                <button
                  onClick={logout}
                  className="flex items-center gap-1 hover:underline"
                >
                  <LogOut className="h-4 w-4" />
                  {t('header.logout')}
                </button>
              </div>
            ) : (
              <>
                <LanguageLink href="/login" className="hover:underline">
                  {t('header.login')}
                </LanguageLink>
                <span className="mx-2">|</span>
                <LanguageLink href="/register" className="hover:underline">
                  {t('header.register')}
                </LanguageLink>
                <span className="mx-2">|</span>
              </>
            )}
            <div className="flex items-center gap-2">
              <Link href={createLanguageLink('zh')} className="hover:underline">
                繁
              </Link>
              <Link href={createLanguageLink('cn')} className="hover:underline">
                简
              </Link>
              <Link href={createLanguageLink('en')} className="hover:underline">
                EN
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="bg-white border-b">
        <div className="container">
          <div className="flex h-20 items-center justify-between">
            <LanguageLink href="/" className="flex items-center gap-3">
              <div className="relative w-16 h-16">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/WhatsApp%20Image%202025-02-25%20at%2014.29.07_509a9aaf.jpg-KDCEr9VlnMpFDB5hTK3jEgOuZmC0A7.jpeg"
                  alt="KCC Logo"
                  fill
                  className="object-contain"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-[#1E1B4B]">{t('header.companyNameShort')}</span>
                {currentLang === 'en' && (
                  <span className="text-sm text-gray-600">{t('header.companyName')}</span>
                )}
              </div>
            </LanguageLink>

            {/* Desktop Navigation */}
            <nav className="hidden lg:block">
              <NavigationMenu>
                <NavigationMenuList>
                  {mainNavigation.map((item) => (
                    <NavigationMenuItem key={item.path}>
                      {item.items ? (
                        <>
                          <NavigationMenuTrigger>
                            <span className="flex items-center gap-1">
                              {getLocalizedTitle(item.title)}
                            </span>
                          </NavigationMenuTrigger>
                          <NavigationMenuContent>
                            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-1">
                              {item.items.map((subItem) => (
                                <li key={subItem.path}>
                                  <NavigationMenuLink asChild>
                                    {(() => {
                                      if (subItem.title.en === "Chamber Activities") {
                                        return (
                                          <div className="grid grid-cols-2 select-none rounded-md p-3 leading-none no-underline outline-none gap-2">
                                            <LanguageLink
                                              href="/events/new-era"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.newEra')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/elderly-activities"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.elderlyActivities')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/youth-festival"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.youthFestival')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/forums"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.forums')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "Suggestion Box + Contact Us") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href="/suggestion"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.suggestionBox')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/contact"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.contactUs')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "Council Member + Past Councils") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href="/about-kcc/council-member"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.councilMember')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/history/councilors"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.pastCouncils')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "KCC Development + Milestones") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href="/about-kcc/development-history"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.kccDevelopment')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/history/milestones"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.milestones')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "Reception + Monthly Meetings") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href="/events/reception"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.reception')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/monthly-meetings"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.monthlyMeetings')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "Social Welfare Activities") {
                                        return (
                                          <div className="grid grid-cols-3 select-none rounded-md p-3 leading-none no-underline outline-none gap-2">
                                            <LanguageLink
                                              href="/events/care-team"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.careTeam')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/about-kcc/elderly-center"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.elderlyCenter')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/sea-scouts"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.seaScouts')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else {
                                        return (
                                          <LanguageLink
                                            href={subItem.path}
                                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                          >
                                            <div className="text-sm font-medium leading-none">{getLocalizedTitle(subItem.title)}</div>
                                            {(subItem as any).subtitle && (
                                              <div className="text-xs text-gray-500 mt-1">
                                                {getLocalizedTitle((subItem as any).subtitle)}
                                              </div>
                                            )}
                                          </LanguageLink>
                                        );
                                      }
                                    })()}
                                  </NavigationMenuLink>
                                </li>
                              ))}
                            </ul>
                          </NavigationMenuContent>
                        </>
                      ) : (
                        <LanguageLink
                          href={item.path}
                          className="flex items-center gap-1 px-4 py-2 hover:text-primary"
                        >
                          {getLocalizedTitle(item.title)}
                        </LanguageLink>
                      )}
                    </NavigationMenuItem>
                  ))}
                </NavigationMenuList>
              </NavigationMenu>
            </nav>

            {/* Mobile Menu Button */}
            <Button variant="ghost" size="icon" className="lg:hidden" onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden border-t">
              <nav className="flex flex-col py-4">
                {mainNavigation.map((item) => (
                  <div key={item.path} className="border-b border-gray-100 last:border-0">
                    <div className="flex items-center justify-between">
                      {item.items ? (
                        <button
                          className="flex-1 block px-4 py-2 hover:bg-gray-50 text-left"
                          onClick={() => toggleExpanded(item.path)}
                        >
                          <div className="font-medium">{getLocalizedTitle(item.title)}</div>
                        </button>
                      ) : (
                        <LanguageLink
                          href={item.path}
                          className="flex-1 block px-4 py-2 hover:bg-gray-50"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <div className="font-medium">{getLocalizedTitle(item.title)}</div>
                        </LanguageLink>
                      )}
                      {item.items && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mr-2"
                          onClick={() => toggleExpanded(item.path)}
                        >
                          <ChevronDown
                            className={`h-4 w-4 transition-transform ${
                              expandedItems.includes(item.path) ? "rotate-180" : ""
                            }`}
                          />
                        </Button>
                      )}
                    </div>
                    {item.items && expandedItems.includes(item.path) && (
                      <div className="bg-gray-50">
                        {item.items.map((subItem) => {
                          if (subItem.title.en === "Chamber Activities") {
                            return (
                              <div key={subItem.path} className="border-b border-gray-100 last:border-0">
                                <LanguageLink
                                  href="/events/new-era"
                                  className="block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('activities.newEra')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/events/elderly-activities"
                                  className="block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('activities.elderlyActivities')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/events/youth-festival"
                                  className="block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('activities.youthFestival')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/events/forums"
                                  className="block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('activities.forums')}</div>
                                </LanguageLink>
                              </div>
                            );
                          } else if (subItem.title.en === "Suggestion Box + Contact Us") {
                            return (
                              <div key={subItem.path} className="flex border-b border-gray-100 last:border-0">
                                <LanguageLink
                                  href="/suggestion"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.suggestionBox')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/contact"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.contactUs')}</div>
                                </LanguageLink>
                              </div>
                            );
                          } else if (subItem.title.en === "Council Member + Past Councils") {
                            return (
                              <div key={subItem.path} className="flex border-b border-gray-100 last:border-0">
                                <LanguageLink
                                  href="/about-kcc/council-member"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.councilMember')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/history/councilors"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.pastCouncils')}</div>
                                </LanguageLink>
                              </div>
                            );
                          } else if (subItem.title.en === "KCC Development + Milestones") {
                            return (
                              <div key={subItem.path} className="flex border-b border-gray-100 last:border-0">
                                <LanguageLink
                                  href="/about-kcc/development-history"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.kccDevelopment')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/history/milestones"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.milestones')}</div>
                                </LanguageLink>
                              </div>
                            );
                          } else if (subItem.title.en === "Reception + Monthly Meetings") {
                            return (
                              <div key={subItem.path} className="flex border-b border-gray-100 last:border-0">
                                <LanguageLink
                                  href="/events/reception"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.reception')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/events/monthly-meetings"
                                  className="flex-1 block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('navigation.monthlyMeetings')}</div>
                                </LanguageLink>
                              </div>
                            );
                          } else if (subItem.title.en === "Social Welfare Activities") {
                            return (
                              <div key={subItem.path} className="border-b border-gray-100 last:border-0">
                                <LanguageLink
                                  href="/events/care-team"
                                  className="block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('activities.careTeam')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/about-kcc/elderly-center"
                                  className="block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('activities.elderlyCenter')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  href="/events/sea-scouts"
                                  className="block px-6 py-2 hover:bg-gray-100"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <div className="font-medium">{t('activities.seaScouts')}</div>
                                </LanguageLink>
                              </div>
                            );
                          } else {
                            return (
                              <LanguageLink
                                key={subItem.path}
                                href={subItem.path}
                                className="block px-6 py-2 hover:bg-gray-100"
                                onClick={() => setIsMenuOpen(false)}
                              >
                                <div className="font-medium">{getLocalizedTitle(subItem.title)}</div>
                                {(subItem as any).subtitle && (
                                  <div className="text-xs text-gray-500">
                                    {getLocalizedTitle((subItem as any).subtitle)}
                                  </div>
                                )}
                              </LanguageLink>
                            );
                          }
                        })}
                      </div>
                    )}
                  </div>
                ))}
              </nav>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}

