"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchKccElites } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Award } from "lucide-react"

interface EliteProgramme {
  id: number
  documentId: string
  title: string
  name?: string
  achievement?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  order?: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: {
    url: string
    formats: {
      thumbnail?: { url: string }
      small?: { url: string }
      medium?: { url: string }
      large?: { url: string }
    }
  }
}

export default function EliteProgrammePage() {
  const [eliteProgrammes, setEliteProgrammes] = useState<EliteProgramme[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadEliteProgrammes = async () => {
      try {
        setLoading(true)
        const response = await fetchKccElites({
          populate: "*"
        })

        if (response && response.data) {
          // Log the elite programme data to debug the structure
          console.log("Elite programme data:", JSON.stringify(response.data, null, 2));

          // Extract elite programmes from the response
          const programmes = Array.isArray(response.data) ? response.data : [response.data];

          if (programmes.length > 0) {
            setEliteProgrammes(programmes);
          } else {
            // If no programmes are found, use fallback data
            setEliteProgrammes(fallbackProgrammes);
          }
        } else {
          // If response is empty, use fallback data
          setEliteProgrammes(fallbackProgrammes);
        }

        setLoading(false)
      } catch (err) {
        console.error("Error loading elite programmes:", err)
        setError("Failed to load elite programmes. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setEliteProgrammes(fallbackProgrammes)
      }
    }

    loadEliteProgrammes()
  }, [])

  // Helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      return content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      });
    }

    return null;
  };

  // Fallback data in case the API fails
  const fallbackProgrammes: EliteProgramme[] = [
    {
      id: 1,
      documentId: "fallback-1",
      title: "KCC精英培訓計劃2023年度優秀學員",
      name: "張明",
      achievement: [
        {
          type: "paragraph",
          children: [
            {
              text: "在2023年度KCC精英培訓計劃中表現卓越，在商業策略、領導力和創新思維方面均獲得高度評價。成功領導團隊完成多個實戰項目，並獲得導師一致好評。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-01-01T00:00:00.000Z",
      updatedAt: "2023-01-01T00:00:00.000Z",
      publishedAt: "2023-01-01T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 2,
      documentId: "fallback-2",
      title: "KCC精英培訓計劃創新獎",
      name: "李華",
      achievement: [
        {
          type: "paragraph",
          children: [
            {
              text: "在KCC精英培訓計劃中提出創新商業模式，獲得創新獎。其提出的電子商務解決方案被評為具有高度市場潛力，並已開始實施試點項目。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-01-01T00:00:00.000Z",
      updatedAt: "2023-01-01T00:00:00.000Z",
      publishedAt: "2023-01-01T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 3,
      documentId: "fallback-3",
      title: "KCC精英培訓計劃領導力獎",
      name: "陳偉",
      achievement: [
        {
          type: "paragraph",
          children: [
            {
              text: "在KCC精英培訓計劃中展現出色領導才能，成功帶領團隊克服多個挑戰，完成高難度項目。其領導風格和團隊管理能力獲得評審團一致讚賞。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-01-01T00:00:00.000Z",
      updatedAt: "2023-01-01T00:00:00.000Z",
      publishedAt: "2023-01-01T00:00:00.000Z",
      locale: "zh"
    }
  ]

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "九龍總商會新世紀精英培訓計劃",
          en: "KCC Elite Development Programme",
        }}
        description={{
          zh: "為新世紀培養商界精英的計劃",
          en: "Programme for cultivating business elites for the new century",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">九龍總商會新世紀精英培訓計劃 / KCC Elite Development Programme</h2>
              <p>
                九龍總商會新世紀精英培訓計劃旨在為香港培養新一代商業領袖，
                提供全面的商業知識、領導力和創新思維培訓。計劃通過課堂學習、
                實戰項目、導師指導和海外考察等多種形式，全方位提升學員的綜合能力。
              </p>
              <p>
                The KCC Elite Development Programme aims to cultivate a new generation of
                business leaders for Hong Kong, providing comprehensive training in business knowledge,
                leadership, and innovative thinking. The programme enhances participants' comprehensive
                abilities through various forms such as classroom learning, practical projects,
                mentor guidance, and overseas visits.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
            {error}
          </div>
        )}

        <h2 className="text-3xl font-bold mb-8">精英學員成就 / Elite Achievers</h2>

        {loading ? (
          // Loading skeleton
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardContent className="p-6">
                  <Skeleton className="h-8 w-3/4 mb-4" />
                  <Skeleton className="h-6 w-1/2 mb-6" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-2" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {eliteProgrammes.length > 0 ? (
              eliteProgrammes.map((programme) => (
                <Card key={programme.id} className="overflow-hidden hover:shadow-lg transition-all">
                  {programme.image && (
                    <div className="relative w-full h-[200px]">
                      <Image
                        src={programme.image.formats?.medium?.url || programme.image.url}
                        alt={programme.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <CardHeader className="bg-primary/5 pb-2">
                    <Badge className="w-fit mb-2">精英學員 / Elite Member</Badge>
                    <CardTitle className="text-xl">{programme.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    {programme.name && (
                      <div className="flex items-center gap-2 mb-4">
                        <Award className="h-5 w-5 text-primary" />
                        <span className="font-semibold text-lg">{programme.name}</span>
                      </div>
                    )}
                    <div className="text-muted-foreground">
                      {renderStructuredContent(programme.achievement)}
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12 col-span-full">
                <p className="text-xl text-muted-foreground">暫無精英學員資料 / No elite member data available</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}