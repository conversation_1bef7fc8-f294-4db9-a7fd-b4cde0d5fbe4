import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ChevronRight, Download } from "lucide-react"
import Link from "next/link"

export default function PublicationsPage() {
  const magazines = [
    {
      id: 1,
      title: "商薈 2024年1月號",
      coverImage: "/placeholder.svg",
      description: "本期內容：香港經濟展望、大灣區商業機遇、會員專訪",
      date: "2024-01-15",
    },
    {
      id: 2,
      title: "商薈 2023年12月號",
      coverImage: "/placeholder.svg",
      description: "本期內容：年度回顧、商會活動總結、新年展望",
      date: "2023-12-15",
    },
    {
      id: 3,
      title: "商薈 2023年11月號",
      coverImage: "/placeholder.svg",
      description: "本期內容：創新科技、商業轉型、國際貿易",
      date: "2023-11-15",
    },
  ]

  const newsletters = [
    {
      id: 1,
      title: "商貿通訊 2024年2月",
      pdfLink: "#",
      description: "最新商業資訊、政策解讀、市場分析",
      date: "2024-02-10",
    },
    {
      id: 2,
      title: "商貿通訊 2024年1月",
      pdfLink: "#",
      description: "新年展望、經濟預測、行業趨勢",
      date: "2024-01-10",
    },
    {
      id: 3,
      title: "商貿通訊 2023年12月",
      pdfLink: "#",
      description: "年終總結、政策回顧、來年展望",
      date: "2023-12-10",
    },
  ]

  const reports = [
    {
      id: 1,
      title: "2023年香港經濟報告",
      pdfLink: "#",
      description: "全面分析香港經濟狀況、挑戰與機遇",
      date: "2023-12-20",
    },
    {
      id: 2,
      title: "大灣區商業機遇研究報告",
      pdfLink: "#",
      description: "深入探討大灣區發展與商業前景",
      date: "2023-10-15",
    },
    {
      id: 3,
      title: "香港中小企業發展調查報告",
      pdfLink: "#",
      description: "分析中小企業面臨的挑戰與發展策略",
      date: "2023-08-30",
    },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[40vh] flex items-center justify-center bg-[#1E1B4B]">
        <div className="absolute inset-0">
          <Image src="/placeholder.svg" alt="Publications" fill className="object-cover opacity-20" />
        </div>
        <div className="relative text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">出版刊物 / Publications</h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto px-4">
            九龍總商會定期出版各類刊物，為會員及公眾提供最新商業資訊及市場分析
          </p>
        </div>
      </section>

      <div className="container py-12">
        <Tabs defaultValue="magazines" className="space-y-8">
          <TabsList className="w-full max-w-md mx-auto">
            <TabsTrigger value="magazines" className="flex-1">
              商薈月刊
            </TabsTrigger>
            <TabsTrigger value="newsletters" className="flex-1">
              商貿通訊
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex-1">
              研究報告
            </TabsTrigger>
          </TabsList>

          {/* Magazines Tab */}
          <TabsContent value="magazines" className="space-y-8">
            <div className="grid md:grid-cols-3 gap-8">
              {magazines.map((magazine) => (
                <Card key={magazine.id} className="overflow-hidden hover:shadow-lg transition-all">
                  <div className="relative aspect-[3/4]">
                    <Image
                      src={magazine.coverImage || "/placeholder.svg"}
                      alt={magazine.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardContent className="p-6">
                    <time className="text-sm text-gray-500">{magazine.date}</time>
                    <h3 className="text-xl font-bold mt-2 mb-3">{magazine.title}</h3>
                    <p className="text-gray-600 mb-4">{magazine.description}</p>
                    <Button className="w-full bg-[#1E1B4B] hover:bg-[#1E1B4B]/90">
                      閱讀更多 <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="flex justify-center">
              <Button variant="outline">查看更多期數</Button>
            </div>
          </TabsContent>

          {/* Newsletters Tab */}
          <TabsContent value="newsletters" className="space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              {newsletters.map((newsletter) => (
                <Card key={newsletter.id} className="hover:shadow-lg transition-all">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <time className="text-sm text-gray-500">{newsletter.date}</time>
                        <h3 className="text-xl font-bold mt-1">{newsletter.title}</h3>
                      </div>
                      <Button variant="outline" size="icon" asChild>
                        <Link href={newsletter.pdfLink}>
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Download</span>
                        </Link>
                      </Button>
                    </div>
                    <p className="text-gray-600 mb-4">{newsletter.description}</p>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href={newsletter.pdfLink}>
                        下載 PDF <Download className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="flex justify-center">
              <Button variant="outline">查看更多通訊</Button>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              {reports.map((report) => (
                <Card key={report.id} className="hover:shadow-lg transition-all">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <time className="text-sm text-gray-500">{report.date}</time>
                        <h3 className="text-xl font-bold mt-1">{report.title}</h3>
                      </div>
                      <Button variant="outline" size="icon" asChild>
                        <Link href={report.pdfLink}>
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Download</span>
                        </Link>
                      </Button>
                    </div>
                    <p className="text-gray-600 mb-4">{report.description}</p>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href={report.pdfLink}>
                        下載報告 <Download className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="flex justify-center">
              <Button variant="outline">查看更多報告</Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

