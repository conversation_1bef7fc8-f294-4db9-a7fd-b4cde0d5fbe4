"use client"

import { useState, useEffect } from "react"
import { fetchCouncilMembers, fetchHome, fetchNews } from "@/lib/strapi"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"

export default function ApiTest() {
  const [homeData, setHomeData] = useState<any>(null)
  const [councilMembers, setCouncilMembers] = useState<any>(null)
  const [newsItems, setNewsItems] = useState<any>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("home")
  const [debugInfo, setDebugInfo] = useState<any>({
    token: process.env.NEXT_PUBLIC_STRAPI_TOKEN ? "Set (first 10 chars: " + process.env.NEXT_PUBLIC_STRAPI_TOKEN.substring(0, 10) + "...)" : "Not set",
    url: process.env.NEXT_PUBLIC_STRAPI_URL || "Default URL"
  })

  // Direct API test function
  const testDirectApi = async () => {
    setLoading(true)
    setError(null)

    try {
      const url = `${process.env.NEXT_PUBLIC_STRAPI_URL || "https://strapibackendproject-3x6s.onrender.com/api"}/i18n/locales`
      const token = "5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae"

      console.log(`Testing direct API call to: ${url}`)

      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      })

      console.log(`Response status: ${response.status} ${response.statusText}`)

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log("API response:", data)

      setHomeData({ directApiTest: data })
    } catch (err: any) {
      console.error("Error testing direct API:", err)
      setError(`Direct API test failed: ${err.message || "Unknown error"}`)
    } finally {
      setLoading(false)
    }
  }

  const fetchData = async (type: string) => {
    setLoading(true)
    setError(null)

    try {
      switch (type) {
        case "home":
          // Try direct API test instead of using the helper function
          await testDirectApi()
          break
        case "council":
          const councilResponse = await fetchCouncilMembers({
            populate: "*",
            sort: "order:asc"
          })
          setCouncilMembers(councilResponse.data)
          break
        case "news":
          // Fetch news items from the home endpoint
          const newsResponse = await fetchNews({
            populate: "*"
          });
          setNewsItems(newsResponse.data);
          break
        default:
          break
      }
    } catch (err: any) {
      console.error(`Error fetching ${type} data:`, err)
      setError(`Failed to load ${type} data: ${err.message || "Unknown error"}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData(activeTab)
  }, [activeTab])

  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  return (
    <div className="container py-12">
      <h1 className="text-3xl font-bold mb-8">Strapi API Integration Test</h1>

      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-8">
        <h2 className="text-xl font-semibold mb-2 text-blue-800">Important Notice</h2>
        <p className="mb-2">
          If you're seeing 404 errors, it means the content types don't exist or have no published content in Strapi.
        </p>
        <p className="mb-4">
          Use the <Link href="/api-check" className="text-blue-600 hover:underline font-medium">API Endpoint Checker</Link> to test different endpoints.
        </p>
        <Button onClick={testDirectApi} disabled={loading}>
          {loading ? "Testing..." : "Test Direct API Call"}
        </Button>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>API Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div><strong>API URL:</strong> {debugInfo.url}</div>
            <div><strong>API Token:</strong> {debugInfo.token}</div>
            <div><strong>Environment:</strong> {process.env.NODE_ENV}</div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="mb-8">
        <TabsList>
          <TabsTrigger value="home">Home</TabsTrigger>
          <TabsTrigger value="council">Council Members</TabsTrigger>
          <TabsTrigger value="news">News</TabsTrigger>
        </TabsList>

        <TabsContent value="home" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Home Page Data</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Loading...</div>
              ) : error ? (
                <div className="text-red-500 p-4 bg-red-50 rounded-md mb-4">{error}</div>
              ) : homeData ? (
                <div>
                  <h2 className="text-xl font-semibold mb-2">{homeData.attributes?.title || "Direct API Test Result"}</h2>
                  <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96">
                    {JSON.stringify(homeData, null, 2)}
                  </pre>
                </div>
              ) : (
                <div>No data available. Click the button below to fetch data.</div>
              )}

              <Button
                onClick={() => fetchData("home")}
                className="mt-4"
                disabled={loading}
              >
                {loading ? "Loading..." : "Refresh Data"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="council" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Council Members</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Loading...</div>
              ) : error ? (
                <div className="text-red-500 p-4 bg-red-50 rounded-md mb-4">{error}</div>
              ) : councilMembers ? (
                <div>
                  <h2 className="text-xl font-semibold mb-2">Council Members ({Array.isArray(councilMembers) ? councilMembers.length : 0})</h2>
                  <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96">
                    {JSON.stringify(councilMembers, null, 2)}
                  </pre>
                </div>
              ) : (
                <div>No data available. Click the button below to fetch data.</div>
              )}

              <Button
                onClick={() => fetchData("council")}
                className="mt-4"
                disabled={loading}
              >
                {loading ? "Loading..." : "Refresh Data"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="news" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Latest News</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Loading...</div>
              ) : error ? (
                <div className="text-red-500 p-4 bg-red-50 rounded-md mb-4">{error}</div>
              ) : newsItems ? (
                <div>
                  <h2 className="text-xl font-semibold mb-2">News Items ({Array.isArray(newsItems) ? newsItems.length : 0})</h2>
                  <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96">
                    {JSON.stringify(newsItems, null, 2)}
                  </pre>
                </div>
              ) : (
                <div>No data available. Click the button below to fetch data.</div>
              )}

              <Button
                onClick={() => fetchData("news")}
                className="mt-4"
                disabled={loading}
              >
                {loading ? "Loading..." : "Refresh Data"}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="mt-8 p-4 bg-muted rounded-md">
        <h2 className="text-xl font-semibold mb-2">Integration Instructions</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Make sure your Strapi backend is running at <code className="bg-background px-1 rounded">https://strapibackendproject-3x6s.onrender.com</code></li>
          <li>Generate an API token in Strapi Admin → Settings → API Tokens</li>
          <li>Add the token to your <code className="bg-background px-1 rounded">.env.local</code> file as <code className="bg-background px-1 rounded">NEXT_PUBLIC_STRAPI_TOKEN</code></li>
          <li>Create at least one entry for each content type in Strapi</li>
          <li>Configure permissions for the Public role in Strapi to allow access to your content</li>
        </ol>
      </div>

      <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <h2 className="text-xl font-semibold mb-2 text-yellow-800">Troubleshooting 404 Errors</h2>
        <p className="mb-4">If you're seeing 404 Not Found errors, check the following:</p>
        <ol className="list-decimal pl-5 space-y-2 text-yellow-700">
          <li>In Strapi Admin, go to Content-Type Builder and verify that the content type exists</li>
          <li>In Content Manager, create and publish at least one entry for each content type</li>
          <li>In Settings → Roles → Public, make sure "find" and "findOne" permissions are enabled for all content types</li>
          <li>Check the API endpoint URL - it should match exactly what's in Strapi</li>
          <li>Try using the <Link href="/api-check" className="text-yellow-600 hover:underline font-medium">API Endpoint Checker</Link> to test different endpoints</li>
        </ol>
      </div>
    </div>
  )
}