"use client"

import { motion } from "framer-motion"

interface SingleLanguageSectionHeaderProps {
  title: string
  description?: string
  align?: "left" | "center" | "right"
}

export default function SingleLanguageSectionHeader({ 
  title, 
  description, 
  align = "left" 
}: SingleLanguageSectionHeaderProps) {
  const alignmentClasses = {
    left: "text-left",
    center: "text-center",
    right: "text-right",
  }

  return (
    <motion.div
      className={`mb-12 ${alignmentClasses[align]}`}
      initial={{ y: 20, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="inline-block">
        <motion.div
          className="h-1 bg-primary mb-4"
          initial={{ width: 0 }}
          whileInView={{ width: "100%" }}
          viewport={{ once: true }}
          transition={{ delay: 0.5, duration: 0.8 }}
        />
        <h2 className="text-3xl font-bold">
          {title}
        </h2>
      </div>
      {description && (
        <motion.p
          className={`mt-4 text-muted-foreground max-w-2xl ${align === 'center' ? 'mx-auto' : ''}`}
          initial={{ y: 20, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.3, duration: 0.8 }}
        >
          {description}
        </motion.p>
      )}
    </motion.div>
  )
}
