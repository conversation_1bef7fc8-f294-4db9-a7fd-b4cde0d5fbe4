import { Noto_Sans_TC } from "next/font/google"
import HeaderWrapper from "@/components/header-wrapper"
import Footer from "@/components/footer"
import { Toaster } from "sonner"
import { UserProvider } from "@/contexts/user-context"
import './globals.css'
import Script from 'next/script'

const notoSansTC = Noto_Sans_TC({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-HK">
      <body className={notoSansTC.className}>
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-RRJJYLYSL9"
          strategy="afterInteractive"
        />
        <Script id="gtag-init" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-RRJJYLYSL9');
          `}
        </Script>
        <UserProvider>
          <HeaderWrapper />
          <main>{children}</main>
          <Footer />
          <Toaster />
        </UserProvider>
      </body>
    </html>
  )
}