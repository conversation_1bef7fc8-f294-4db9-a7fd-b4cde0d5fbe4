import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { getArticles } from "@/lib/strapi"

export default async function StrapiDemoPage() {
  // Fetch articles from Strapi
  const articlesResponse = await getArticles()

  return (
    <div className="container py-12">
      <Card>
        <CardHeader>
          <CardTitle>Strapi Integration Demo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Articles from Strapi:</h3>

              {articlesResponse.data && articlesResponse.data.length > 0 ? (
                <div className="grid gap-4">
                  {articlesResponse.data.map((article: any) => (
                    <div key={article.id} className="p-4 border rounded-md">
                      <h4 className="font-medium">{article.attributes.title}</h4>
                      <p className="text-sm text-muted-foreground">{article.attributes.description}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-4 bg-muted rounded-md">
                  <p>No articles found in Strapi.</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    To see data here, you need to create some articles in your Strapi admin panel.
                  </p>
                </div>
              )}
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="font-medium text-blue-800 mb-2">Next Steps:</h3>
              <ol className="list-decimal list-inside space-y-2 text-blue-700">
                <li>
                  Update your STRAPI_URL environment variable to:{" "}
                  <code className="bg-blue-100 px-1 py-0.5 rounded">
                    https://ecommerce-backend-g24m.onrender.com/api
                  </code>
                </li>
                <li>Create content types in your Strapi admin panel (articles, news, events, etc.)</li>
                <li>Add some content to these content types</li>
                <li>Update the website components to fetch and display real data from Strapi</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

