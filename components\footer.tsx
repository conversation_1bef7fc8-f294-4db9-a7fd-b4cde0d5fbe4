import { Suspense } from "react"
import FooterContent from "./footer-content"

function FooterFallback() {
  return (
    <footer className="bg-[#1E1B4B] text-white">
      <div className="container mx-auto">
        <div className="py-16">
          <div className="animate-pulse">
            <div className="flex flex-col md:flex-row justify-between items-start py-12 border-b border-blue-800">
              <div className="mb-8 md:mb-0">
                <div className="w-32 h-32 bg-blue-700 rounded mb-6"></div>
              </div>
              <div className="space-y-4">
                <div className="h-4 bg-blue-700 rounded w-64"></div>
                <div className="h-4 bg-blue-700 rounded w-56"></div>
                <div className="h-4 bg-blue-700 rounded w-72"></div>
              </div>
            </div>
            <div className="py-8 border-b border-blue-800">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Array(7).fill(null).map((_, i) => (
                  <div key={i} className="h-12 bg-blue-700 rounded"></div>
                ))}
              </div>
            </div>
            <div className="py-6 flex flex-col md:flex-row justify-between items-center">
              <div className="h-4 bg-blue-700 rounded w-48"></div>
              <div className="flex gap-4">
                <div className="h-4 bg-blue-700 rounded w-16"></div>
                <div className="h-4 bg-blue-700 rounded w-16"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default function Footer() {
  return (
    <Suspense fallback={<FooterFallback />}>
      <FooterContent />
    </Suspense>
  )
}
