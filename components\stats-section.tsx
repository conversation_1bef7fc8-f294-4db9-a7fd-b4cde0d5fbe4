"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"

const stats = [
  {
    number: "53",
    label: "年會員數目",
    sublabel: "Years of Membership",
    icon: "🏢",
  },
  {
    number: "2",
    label: "2023年度傑出企業大獎",
    sublabel: "Outstanding Enterprise Awards 2023",
    icon: "🏆",
  },
  {
    number: "17",
    label: "2023年度傑出會員",
    sublabel: "Outstanding Members 2023",
    icon: "👥",
  },
]

export default function StatsSection() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {stats.map((stat, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: i * 0.2 }}
          viewport={{ once: true }}
        >
          <Card className="text-center hover:shadow-lg transition-all duration-300">
            <CardContent className="p-8">
              <span className="text-4xl mb-4 block">{stat.icon}</span>
              <h3 className="text-5xl font-bold text-[#002B5C] mb-4">{stat.number}</h3>
              <p className="text-gray-600">{stat.label}</p>
              <p className="text-sm text-gray-500">{stat.sublabel}</p>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

