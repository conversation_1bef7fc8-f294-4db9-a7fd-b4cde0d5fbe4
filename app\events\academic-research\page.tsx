"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"

import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchAcademicResearches } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Calendar, Users } from "lucide-react"

interface AcademicResearch {
  id: number
  documentId: string
  title: string
  researchers?: string
  abstract?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  fullText?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  publicationDate?: string
  media?: {
    url: string
    alternativeText?: string
    formats?: {
      large?: { url: string }
      medium?: { url: string }
      small?: { url: string }
      thumbnail?: { url: string }
    }
  }[]
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
}

export default function AcademicResearchPage() {
  const [academicResearches, setAcademicResearches] = useState<AcademicResearch[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    const loadAcademicResearches = async () => {
      try {
        setLoading(true)
        const response = await fetchAcademicResearches({
          populate: "*"
        })

        if (response && response.data) {
          // Log the academic research data to debug the structure
          console.log("Academic research data:", JSON.stringify(response.data, null, 2));

          // Extract academic research from the response
          const researches = Array.isArray(response.data) ? response.data : [response.data];

          if (researches.length > 0) {
            setAcademicResearches(researches);
            setDebugInfo(`Found ${researches.length} academic research items.`);
          } else {
            // If no researches are found, use fallback data
            setAcademicResearches(fallbackResearches);
            setDebugInfo("No academic research items found in API response. Using fallback data.");
          }
        } else {
          // If response is empty, use fallback data
          setAcademicResearches(fallbackResearches);
          setDebugInfo("Empty API response. Using fallback data.");
        }

        setLoading(false)
      } catch (err) {
        console.error("Error loading academic researches:", err)
        setError("Failed to load academic researches. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setAcademicResearches(fallbackResearches)
        setDebugInfo(`API error: ${err}. Using fallback data.`);
      }
    }

    loadAcademicResearches()
  }, [])

  // Helper function to get image URL from media array
  const getImageUrl = (research: AcademicResearch) => {
    if (research.media && research.media.length > 0) {
      const media = research.media[0];
      return media.formats?.medium?.url || media.formats?.small?.url || media.url || "/placeholder.svg";
    }
    return "/placeholder.svg";
  }

  // Helper function to get image alt text
  const getImageAlt = (research: AcademicResearch) => {
    if (research.media && research.media.length > 0 && research.media[0].alternativeText) {
      return research.media[0].alternativeText;
    }
    return research.title || "Academic Research";
  }

  // Helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      return content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      });
    }

    return null;
  };

  // Fallback data in case the API fails
  const fallbackResearches: AcademicResearch[] = [
    {
      id: 1,
      documentId: "fallback-1",
      title: "Advancements in AI-Based Disease Diagnosis",
      researchers: "Dr. John Doe, Dr. Jane Smith, Dr. Emily Brown",
      abstract: [
        {
          type: "paragraph",
          children: [
            {
              text: "Artificial Intelligence (AI) has revolutionized medical diagnostics by enhancing accuracy and efficiency. This research explores the application of machine learning algorithms in early disease detection, particularly for cancer, Alzheimer's, and cardiovascular diseases.",
              type: "text"
            }
          ]
        }
      ],
      fullText: [
        {
          type: "paragraph",
          children: [
            {
              text: "With the increasing availability of medical data, AI-driven diagnostic systems have gained significant attention. This paper provides a detailed analysis of how AI is transforming the healthcare industry, from automating radiology reports to predicting patient deterioration based on real-time data.",
              type: "text"
            }
          ]
        }
      ],
      publicationDate: "2025-03-01",
      createdAt: "2023-11-25T00:00:00.000Z",
      updatedAt: "2023-11-25T00:00:00.000Z",
      publishedAt: "2023-11-25T00:00:00.000Z",
      locale: "en"
    },
    {
      id: 2,
      documentId: "fallback-2",
      title: "數字經濟與企業轉型研究",
      researchers: "香港科技大學商學院教授王某、騰訊研究院chief研究員張某",
      abstract: [
        {
          type: "paragraph",
          children: [
            {
              text: "本研究探討數字經濟時代企業如何進行數字化轉型。研究分析了數字化轉型的成功案例、挑戰和策略，為企業提供實用的轉型指導。",
              type: "text"
            }
          ]
        }
      ],
      fullText: [
        {
          type: "paragraph",
          children: [
            {
              text: "隨著數字技術的快速發展，企業數字化轉型已成為必然趨勢。本研究通過對多家成功實現數字化轉型的企業案例分析，總結出數字化轉型的關鍵成功因素和常見挑戰，並提出針對性的轉型策略建議。",
              type: "text"
            }
          ]
        }
      ],
      publicationDate: "2023-10-10",
      createdAt: "2023-10-10T00:00:00.000Z",
      updatedAt: "2023-10-10T00:00:00.000Z",
      publishedAt: "2023-10-10T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 3,
      documentId: "fallback-3",
      title: "後疫情時代的國際貿易新格局研究",
      researchers: "香港理工大學國際貿易學教授黃某、香港貿發局研究部主管劉某",
      abstract: [
        {
          type: "paragraph",
          children: [
            {
              text: "本研究探討後疫情時代國際貿易的新趨勢和香港的角色定位。研究分析了全球供應鏈重構、區域全面經濟夥伴關係協定(RCEP)的影響以及香港如何把握新機遇。",
              type: "text"
            }
          ]
        }
      ],
      fullText: [
        {
          type: "paragraph",
          children: [
            {
              text: "Criteria for global trade's growth has changed significantly. This paper provides a detailed analysis of how AI is transforming the healthcare industry, from automating radiology reports to predicting patient deterioration based on real-time data.",
              type: "text"
            }
          ]
        }
      ],
      publicationDate: "2023-09-05",
      createdAt: "2023-09-05T00:00:00.000Z",
      updatedAt: "2023-09-05T00:00:00.000Z",
      publishedAt: "2023-09-05T00:00:00.000Z",
      locale: "zh"
    }
  ]

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "學術研討",
          en: "Academic Research",
        }}
        description={{
          zh: "九龍總商會舉辦的學術研討會",
          en: "Academic seminars organized by the Kowloon Chamber of Commerce",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">學術研討 / Academic Research</h2>
              <p>
                九龍總商會定期舉辦各類學術研討會，邀請知名學者和專家分享
                最新的研究成果和見解，探討商業發展趨勢和學術前沿。
                這些活動旨在促進學術交流，為會員提供最新的行業資訊和知識。
              </p>
              <p>
                The Kowloon Chamber of Commerce regularly organizes various academic seminars,
                inviting renowned scholars and experts to share the latest research results and insights,
                and to discuss business development trends and academic frontiers.
                These activities aim to promote academic exchanges and provide members with
                the latest industry information and knowledge.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
            <p>{error}</p>
            {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
          </div>
        )}

        {loading ? (
          // Loading skeleton
          <div className="space-y-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-3 gap-0">
                    <Skeleton className="h-64 w-full" />
                    <div className="md:col-span-2 p-6 md:p-8">
                      <Skeleton className="h-8 w-3/4 mb-3" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-2/3 mb-6" />

                      <div className="grid sm:grid-cols-2 gap-4 mb-6">
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-full sm:col-span-2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-8">
            {academicResearches.length > 0 ? (
              academicResearches.map((research) => (
                <Card key={research.id} className="overflow-hidden hover:shadow-lg transition-all">
                  <CardContent className="p-6 md:p-8">
                    <div className="flex flex-col md:flex-row gap-6">
                      {research.media && research.media.length > 0 && (
                        <div className="md:w-1/3">
                          <div className="relative aspect-video md:aspect-square rounded-lg overflow-hidden">
                            <Image
                              src={getImageUrl(research)}
                              alt={getImageAlt(research)}
                              fill
                              className="object-cover"
                            />
                          </div>
                        </div>
                      )}
                      <div className={research.media && research.media.length > 0 ? "md:w-2/3" : "w-full"}>
                        <h3 className="text-2xl font-bold mb-3">{research.title}</h3>

                        {research.abstract && (
                          <div className="text-muted-foreground mb-6">
                            <h4 className="text-lg font-semibold text-foreground mb-2">Abstract</h4>
                            {renderStructuredContent(research.abstract)}
                          </div>
                        )}

                        <div className="grid sm:grid-cols-2 gap-4 mb-6">
                          {research.publicationDate && (
                            <div className="flex items-center gap-2">
                              <Calendar className="h-5 w-5 text-primary" />
                              <span>Published: {research.publicationDate}</span>
                            </div>
                          )}
                          {research.researchers && (
                            <div className="flex items-center gap-2 sm:col-span-2">
                              <Users className="h-5 w-5 text-primary" />
                              <span>Researchers: {research.researchers}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <p className="text-xl text-muted-foreground">暫無學術研討活動資料 / No academic research items available</p>
                {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}