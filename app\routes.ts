// Define all routes for type safety and easy management
export const routes = {
  home: {
    index: "/",
    news: "/news",
    introduction: "/about-kcc/introduction",
    management: "/about-kcc/management"
  },
  about: {
    index: "/about-kcc",
    chamber: "/about-kcc/our-chamber",
    organization: "/about-kcc/chamber-org",
    council: "/about-kcc/council-member",
    affiliated: "/about-kcc/affiliated-associations",
    development: "/about-kcc/development-history",
    inscriptions: "/about-kcc/vip-inscriptions",
    scholarship: "/about-kcc/scholarship",
    elderly: "/about-kcc/elderly-center",
    interviews: "/about-kcc/kcc-interviews",
    anniversary: "/about-kcc/kcc-75th",
    join: "/about-kcc/join-us"
  },
  history: {
    index: "/history",
    milestones: "/history/milestones",
    councilors: "/history/councilors",
    speeches: "/history/key-speeches",
    archives: "/history/archives"
  },
  events: {
    index: "/events",
    upcoming: "/events/upcoming",
    chamber: "/events/chamber-activities",
    reception: "/events/reception-monthly-meetings",
    exchange: "/events/exchange-visits",
    affiliated: "/events/affiliated-activities",
    welfare: "/events/social-welfare",
    other: "/events/other-activities",
    news: "/news",
    monthlyMeetings: "/events/monthly-meetings",
    careTeam: "/events/care-team",
    seaScouts: "/events/sea-scouts"
  },
  contact: {
    index: "/contact",
    join: "/about-kcc/join-us",
    suggestion: "/suggestion",
    rental: "/rental"
  },
  membership: "/membership"
} as const

// Navigation structure
export const mainNavigation = [
  {
    title: { zh: "主頁", en: "Home" },
    path: routes.home.index,
    items: [
      { title: { en: "Latest News", zh: "最新消息" }, path: routes.home.news },
      { title: { en: "What is KCC", zh: "商會簡介" }, path: routes.home.introduction },
      { title: { en: "Management Team", zh: "管理團隊" }, path: routes.home.management }
    ]
  },
  {
    title: { zh: "關於本會", en: "About KCC" },
    path: routes.about.index,
    items: [
      { title: { en: "Our Chamber", zh: "商會簡介" }, path: routes.about.chamber },
      { title: { en: "Chamber Org", zh: "商會架構" }, path: routes.about.organization },
      { title: { en: "Council Member + Past Councils", zh: "理監事會名單+歷屆理監事明表" }, path: routes.about.council },
      { title: { en: "Affiliated Association", zh: "屬會名單" }, path: routes.about.affiliated },
      { title: { en: "KCC Development + Milestones", zh: "龍總發展史簡+本會發展里程" }, path: routes.about.development }
    ]
  },
  {
    title: { zh: "活動項目", en: "Events" },
    path: routes.events.index,
    items: [
      { title: { en: "Upcoming Events", zh: "活動預告" }, path: routes.events.upcoming },
      { title: { en: "Chamber Activities", zh: "商會活動先簡介" }, path: routes.events.chamber },
      { title: { en: "Reception + Monthly Meetings", zh: "接待禮賓+月會" }, path: routes.events.reception },
      { title: { en: "Exchange Visits", zh: "外訪交流" }, path: routes.events.exchange },
      { title: { en: "Affiliated Activities", zh: "屬會活動" }, path: routes.events.affiliated },
      { title: { en: "Social Welfare Activities", zh: "社會公益活動" }, path: routes.events.welfare },
      { title: { en: "Other Activities", zh: "其它活動" }, path: routes.events.other },
      { title: { en: "News Center", zh: "新聞中心" }, path: routes.events.news }
    ]
  },
  {
    title: { zh: "聯絡我們", en: "Contact Us" },
    path: routes.contact.index,
    items: [
      { title: { en: "Join Our Association", zh: "加入本會", subtitle: { en: "Friends of the Dragon Alumni Association", zh: "龍總之友" } }, path: routes.contact.join },
      { title: { en: "Suggestion Box + Contact Us", zh: "意見箱+聯絡我們" }, path: routes.contact.suggestion },
      { title: { en: "Rental Units", zh: "出租單位" }, path: routes.contact.rental }
    ]
  }
]

