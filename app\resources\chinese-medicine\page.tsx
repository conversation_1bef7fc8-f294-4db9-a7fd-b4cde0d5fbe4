import Image from "next/image"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Download, ExternalLink, ChevronRight, Book, Newspaper, Calendar } from "lucide-react"
import Link from "next/link"
import Breadcrumb from "@/components/breadcrumb"
import AnimatedHero from "@/components/animated-hero"

const breadcrumbItems = [
  { title: { en: "Home", zh: "主頁" }, path: "/" },
  { title: { en: "Resources", zh: "資源" }, path: "/resources" },
  { title: { en: "Chinese Medicine Development", zh: "中醫中藥發展資訊" }, path: "/resources/chinese-medicine" },
]

const resources = {
  publications: [
    {
      id: 1,
      title: "中醫藥發展趨勢報告 2024",
      titleEn: "Chinese Medicine Development Trends Report 2024",
      type: "研究報告",
      date: "2024-02",
      description: "深入分析香港中醫藥發展現況、機遇與挑戰",
      image: "/placeholder.svg",
      downloadUrl: "#",
    },
    {
      id: 2,
      title: "大灣區中醫藥產業合作指南",
      titleEn: "Greater Bay Area TCM Industry Cooperation Guide",
      type: "指南",
      date: "2024-01",
      description: "探討大灣區中醫藥產業發展機遇與合作模式",
      image: "/placeholder.svg",
      downloadUrl: "#",
    },
  ],
  news: [
    {
      id: 1,
      title: "政府推出中醫藥發展資助計劃",
      date: "2024-02-20",
      source: "香港政府新聞網",
      link: "#",
    },
    {
      id: 2,
      title: "本會舉辦中醫藥發展論壇",
      date: "2024-02-15",
      source: "九龍總商會",
      link: "#",
    },
  ],
  events: [
    {
      id: 1,
      title: "2024中醫藥發展機遇論壇",
      date: "2024-03-15",
      time: "14:00 - 17:00",
      location: "九龍總商會大樓",
      status: "即將舉行",
    },
    {
      id: 2,
      title: "大灣區中醫藥產業考察團",
      date: "2024-04-10",
      time: "09:00 - 18:00",
      location: "大灣區",
      status: "報名中",
    },
  ],
}

export default function ChineseMedicinePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "中醫中藥發展資訊",
          en: "Chinese Medicine Development",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbItems} />

        {/* Main Content */}
        <div className="mt-8">
          <Tabs defaultValue="publications" className="space-y-8">
            <TabsList className="w-full max-w-md mx-auto">
              <TabsTrigger value="publications" className="flex-1">
                <Book className="w-4 h-4 mr-2" />
                出版刊物
              </TabsTrigger>
              <TabsTrigger value="news" className="flex-1">
                <Newspaper className="w-4 h-4 mr-2" />
                相關新聞
              </TabsTrigger>
              <TabsTrigger value="events" className="flex-1">
                <Calendar className="w-4 h-4 mr-2" />
                活動資訊
              </TabsTrigger>
            </TabsList>

            {/* Publications Tab */}
            <TabsContent value="publications" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {resources.publications.map((pub) => (
                  <Card key={pub.id} className="overflow-hidden hover:shadow-lg transition-all">
                    <div className="relative aspect-[2/1]">
                      <Image src={pub.image || "/placeholder.svg"} alt={pub.title} fill className="object-cover" />
                      <Badge className="absolute top-4 left-4 bg-primary">{pub.type}</Badge>
                    </div>
                    <CardContent className="p-6">
                      <time className="text-sm text-muted-foreground">{pub.date}</time>
                      <h3 className="text-xl font-bold mt-2 mb-1">
                        {pub.title}
                        <span className="block text-base font-normal text-muted-foreground">{pub.titleEn}</span>
                      </h3>
                      <p className="text-muted-foreground mb-4">{pub.description}</p>
                      <div className="flex gap-3">
                        <Button variant="outline" asChild>
                          <Link href={pub.downloadUrl}>
                            <Download className="w-4 h-4 mr-2" />
                            下載 PDF
                          </Link>
                        </Button>
                        <Button>
                          閱讀更多 <ChevronRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* News Tab */}
            <TabsContent value="news">
              <Card>
                <CardHeader>
                  <CardTitle>最新動態</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {resources.news.map((news) => (
                      <div key={news.id} className="flex items-start gap-4 group">
                        <div className="w-2 h-2 mt-2 rounded-full bg-primary flex-shrink-0" />
                        <div>
                          <time className="text-sm text-muted-foreground">{news.date}</time>
                          <h3 className="font-medium group-hover:text-primary">{news.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-muted-foreground">{news.source}</span>
                            <Button variant="link" className="h-auto p-0" asChild>
                              <Link href={news.link}>
                                閱讀全文 <ExternalLink className="w-4 h-4 ml-1" />
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Events Tab */}
            <TabsContent value="events">
              <div className="space-y-6">
                {resources.events.map((event) => (
                  <Card key={event.id} className="hover:shadow-lg transition-all">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-6">
                        <div className="text-center px-4 py-2 bg-primary/10 rounded-lg">
                          <div className="text-2xl font-bold text-primary">{event.date.split("-")[2]}</div>
                          <div className="text-sm text-primary">{event.date.split("-")[1]}月</div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-start justify-between gap-4">
                            <div>
                              <h3 className="text-xl font-bold mb-2">{event.title}</h3>
                              <div className="space-y-1 text-sm text-muted-foreground">
                                <div>時間：{event.time}</div>
                                <div>地點：{event.location}</div>
                              </div>
                            </div>
                            <Badge variant="secondary" className="bg-primary/10 text-primary">
                              {event.status}
                            </Badge>
                          </div>
                          <div className="mt-4">
                            <Button>
                              活動詳情 <ChevronRight className="w-4 h-4 ml-2" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

