"use client"

import { motion } from "framer-motion"

interface LoadingLogoProps {
  className?: string
}

export default function LoadingLogo({ className = "" }: LoadingLogoProps) {
  return (
    <div className={className}>
      <motion.div
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{
          duration: 0.8,
          ease: "easeOut",
        }}
        className="w-24 h-24 bg-white rounded-full flex items-center justify-center"
      >
        <motion.div
          initial={{ rotate: 0 }}
          animate={{ rotate: 360 }}
          transition={{
            duration: 2,
            ease: "linear",
            repeat: Number.POSITIVE_INFINITY,
          }}
          className="w-20 h-20 border-4 border-[#002B5C] rounded-full border-t-transparent"
        />
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.4, duration: 0.4 }}
          className="absolute text-2xl font-bold text-[#002B5C]"
        >
          KCC
        </motion.div>
      </motion.div>
    </div>
  )
}

