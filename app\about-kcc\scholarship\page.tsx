"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"

interface ScholarshipData {
  id: number;
  documentId: string;
  title: string;
  description?: any[];
  criteria?: any[];
  content?: {
    type: string;
    children: {
      text: string;
      type: string;
    }[];
  }[];
  image?: {
    url?: string;
    formats?: {
      large?: {
        url: string;
      };
    };
  };
  order?: string;
}

export default function Scholarship() {
  const [scholarshipData, setScholarshipData] = useState<ScholarshipData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    async function fetchData() {
      try {
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae';

        const response = await fetch(`${strapiUrl}/scholarships?populate=*`, {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          // Map all scholarship data
          const scholarships = data.data.map((item: any) => {
            return item.attributes || item;
          });
          setScholarshipData(scholarships);
          setDebugInfo(`Found ${scholarships.length} scholarships.`);
        } else {
          setError('No scholarship data found.');
        }
      } catch (error: any) {
        console.error('Error fetching scholarship data:', error);
        setError(`Failed to load data: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const renderContent = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p>No content available</p>;
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        );
      }
      return null;
    });
  };

  const renderCriteria = (criteria: any[]) => {
    if (!criteria || !Array.isArray(criteria)) {
      return <p>No criteria available</p>;
    }

    return (
      <ul className="list-disc pl-5 space-y-2">
        {criteria.map((item, index) => (
          <li key={index}>
            {item.type === "paragraph"
              ? item.children.map((child: any) => child.text).join('')
              : item}
          </li>
        ))}
      </ul>
    );
  };

  const getImageUrl = (scholarship: ScholarshipData) => {
    if (!scholarship || !scholarship.image) return "/placeholder.svg";
    return (
      scholarship.image.formats?.large?.url ||
      scholarship.image.url ||
      "/placeholder.svg"
    );
  };

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "獎學金",
          en: "Scholarship",
        }}
        description={{
          zh: "關於我們的獎學金計劃",
          en: "Information about our scholarship programs",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">獎學金 / Scholarship</h2>
              <p>
                九龍總商會設立獎學金計劃，旨在鼓勵青年學子努力學習，培養未來人才。
                我們相信教育是社會發展的基石，通過提供獎學金，為有需要的學生提供支持，
                幫助他們實現教育目標和人生抵負。
              </p>
              <p>
                The Kowloon Chamber of Commerce has established scholarship programs to encourage young students to study hard and cultivate future talents.
                We believe that education is the cornerstone of social development. By providing scholarships, we offer support to students in need,
                helping them achieve their educational goals and life aspirations.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardContent className="p-8">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            ) : scholarshipData.length > 0 ? (
              <div className="space-y-16">
                {scholarshipData.map((scholarship, index) => (
                  <div key={scholarship.id} className="prose prose-lg max-w-none">
                    {index > 0 && <hr className="my-8" />}
                    <h1 className="text-3xl font-bold mb-2">{scholarship.title}</h1>

                    <div className="grid md:grid-cols-2 gap-8 items-start">
                      <div className="space-y-4">
                        {scholarship.description && renderContent(scholarship.description)}
                        {scholarship.content && renderContent(scholarship.content)}

                        {scholarship.criteria && (
                          <div className="mt-6">
                            <h3 className="text-xl font-semibold mb-3">Eligibility Criteria</h3>
                            {renderCriteria(scholarship.criteria)}
                          </div>
                        )}
                      </div>
                      <div className="relative aspect-video mt-6 md:mt-0">
                        <Image
                          src={getImageUrl(scholarship)}
                          alt={scholarship.title || "Scholarship"}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
