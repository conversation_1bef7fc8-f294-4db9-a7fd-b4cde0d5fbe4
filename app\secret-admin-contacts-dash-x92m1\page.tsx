'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

interface Registration {
  id: number;
  documentId: string;
  Name: string;
  email: string;
  Number: string;
  EventName: string;
  OrganizationName: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
}

interface GroupedRegistrations {
  [key: string]: Registration[];
}

export default function ContactSubmissionsPage() {
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());

  useEffect(() => {
    const fetchRegistrations = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/registrations`, {
          headers: {
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to authenticate or fetch data');
        }

        const data = await response.json();
        setRegistrations(data.data);
        setLoading(false);
      } catch (err) {
        console.error('Fetch error:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch registrations');
        setLoading(false);
      }
    };

    fetchRegistrations();
  }, []);

  const groupedRegistrations = registrations.reduce((groups: GroupedRegistrations, reg) => {
    if (!groups[reg.EventName]) {
      groups[reg.EventName] = [];
    }
    groups[reg.EventName].push(reg);
    return groups;
  }, {});

  const toggleEvent = (eventName: string) => {
    setExpandedEvents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(eventName)) {
        newSet.delete(eventName);
      } else {
        newSet.add(eventName);
      }
      return newSet;
    });
  };

  const downloadCSV = () => {
    if (registrations.length === 0) {
      alert('沒有資料可下載 / No data to download');
      return;
    }

    // Create CSV headers
    const headers = [
      'ID',
      'Name / 姓名',
      'Email / 電郵',
      'Phone / 電話',
      'Event Name / 活動名稱',
      'Organization / 機構',
      'Submitted Date / 提交日期'
    ];

    // Convert registrations to CSV format
    const csvData = registrations.map(reg => [
      reg.id,
      reg.Name,
      reg.email,
      reg.Number,
      reg.EventName,
      reg.OrganizationName,
      new Date(reg.createdAt).toLocaleDateString()
    ]);

    // Combine headers and data
    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `event-registrations-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const downloadEventCSV = (eventName: string, eventRegistrations: Registration[]) => {
    if (eventRegistrations.length === 0) {
      alert('此活動沒有報名資料 / No registrations for this event');
      return;
    }

    // Create CSV headers (without Event Name since it's the same for all)
    const headers = [
      'ID',
      'Name / 姓名',
      'Email / 電郵',
      'Phone / 電話',
      'Organization / 機構',
      'Submitted Date / 提交日期'
    ];

    // Convert ONLY the event registrations to CSV format
    const csvData = eventRegistrations.map(reg => [
      reg.id,
      reg.Name,
      reg.email,
      reg.Number,
      reg.OrganizationName,
      new Date(reg.createdAt).toLocaleDateString()
    ]);

    // Combine headers and data
    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${eventName.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '_')}-registrations-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#1E1B4B]"></div>
        <span className="ml-3 text-gray-700">
          <span className="block">正在加載報名資料...</span>
          <span className="text-sm text-gray-500">Loading registrations...</span>
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100 max-w-md">
          <div className="text-red-700 font-medium mb-2">{error}</div>
          <div className="text-red-600 text-sm mb-4">
            <span className="block">請檢查您的API配置並嘗試刷新頁面</span>
            <span className="block text-red-500 mt-1">Please check your API configuration and try refreshing the page</span>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors w-full text-center"
          >
            刷新頁面 / Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="flex justify-between items-start mb-6">
        <h1 className="text-3xl font-bold">
          <span className="block mb-1">活動報名提交記錄</span>
          <span className="text-xl text-gray-600">Event Registration Submissions</span>
        </h1>
        <Button
          onClick={downloadCSV}
          className="bg-[#1E1B4B] hover:bg-[#1E1B4B]/90 text-white"
          disabled={registrations.length === 0}
        >
          <Download className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">下載 CSV / Download CSV</span>
          <span className="sm:hidden">CSV</span>
        </Button>
      </div>

      {/* Summary Section */}
      <div className="bg-gradient-to-r from-[#1E1B4B]/5 to-[#1E1B4B]/10 rounded-lg p-4 mb-6 border border-[#1E1B4B]/20">
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center">
            <span className="font-medium text-[#1E1B4B]">總報名數 / Total Registrations:</span>
            <span className="ml-2 bg-[#1E1B4B] text-white px-2 py-1 rounded text-xs font-bold">
              {registrations.length}
            </span>
          </div>
          <div className="flex items-center">
            <span className="font-medium text-[#1E1B4B]">活動數量 / Events:</span>
            <span className="ml-2 bg-[#1E1B4B] text-white px-2 py-1 rounded text-xs font-bold">
              {Object.keys(groupedRegistrations).length}
            </span>
          </div>
        </div>
      </div>
      <div className="space-y-6">
        {Object.entries(groupedRegistrations).map(([eventName, eventRegistrations]) => (
          <div key={eventName} className="bg-white rounded-lg shadow-md border border-gray-100">
            <div className="px-6 py-4 bg-gray-50 rounded-t-lg flex justify-between items-center">
              <button
                onClick={() => toggleEvent(eventName)}
                className="flex-1 flex justify-between items-center hover:bg-gray-100 rounded transition-colors p-2 -m-2"
              >
                <div>
                  <h2 className="text-xl font-semibold text-[#1E1B4B]">{eventName}</h2>
                  <p className="text-sm text-gray-600">{eventRegistrations.length} 份報名 / {eventRegistrations.length} registrations</p>
                </div>
                <svg
                  className={`w-6 h-6 transition-transform ${expandedEvents.has(eventName) ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              <Button
                onClick={(e) => {
                  e.stopPropagation();
                  downloadEventCSV(eventName, eventRegistrations);
                }}
                size="sm"
                variant="outline"
                className="ml-4 border-[#1E1B4B] text-[#1E1B4B] hover:bg-[#1E1B4B] hover:text-white"
              >
                <Download className="w-3 h-3 mr-1" />
                <span className="hidden sm:inline">CSV</span>
              </Button>
            </div>

            {expandedEvents.has(eventName) && (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 border-collapse">
                  <thead className="bg-[#1E1B4B]/5">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">姓名 / Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">電郵 / Email</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">電話 / Phone</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">機構 / Organization</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">提交日期 / Submitted</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {eventRegistrations.map((reg) => (
                      <tr key={reg.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap font-medium">{reg.Name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-700">{reg.email}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-700">{reg.Number}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-700">{reg.OrganizationName}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-700">
                          {new Date(reg.createdAt).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}