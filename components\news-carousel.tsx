"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { ChevronLeft, ChevronRight } from "lucide-react"

const news = [
  {
    id: 1,
    title: "商會簡介",
    description: "About KCC",
    image: "/placeholder.svg",
  },
  {
    id: 2,
    title: "本會發展里程",
    description: "Development History",
    image: "/placeholder.svg",
  },
  {
    id: 3,
    title: "活動預告",
    description: "Upcoming Events",
    image: "/placeholder.svg",
  },
]

export default function NewsCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0)

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % news.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + news.length) % news.length)
  }

  return (
    <div className="relative">
      <div className="overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {news.map((item) => (
            <div key={item.id} className="w-full flex-shrink-0">
              <Card className="mx-4">
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-2 gap-0">
                    <div className="relative aspect-video md:aspect-square">
                      <Image src={item.image || "/placeholder.svg"} alt={item.title} fill className="object-cover" />
                    </div>
                    <div className="p-8 flex flex-col justify-center">
                      <h3 className="text-2xl font-bold mb-4 text-[#002B5C]">{item.title}</h3>
                      <p className="text-gray-600 mb-6">{item.description}</p>
                      <Button variant="outline">Read More</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      <Button variant="ghost" size="icon" className="absolute left-0 top-1/2 -translate-y-1/2" onClick={prevSlide}>
        <ChevronLeft className="h-6 w-6" />
      </Button>

      <Button variant="ghost" size="icon" className="absolute right-0 top-1/2 -translate-y-1/2" onClick={nextSlide}>
        <ChevronRight className="h-6 w-6" />
      </Button>

      <div className="flex justify-center mt-4 gap-2">
        {news.map((_, i) => (
          <button
            key={i}
            className={`h-2 w-2 rounded-full transition-all ${i === currentSlide ? "bg-[#002B5C] w-4" : "bg-gray-300"}`}
            onClick={() => setCurrentSlide(i)}
          />
        ))}
      </div>
    </div>
  )
}

