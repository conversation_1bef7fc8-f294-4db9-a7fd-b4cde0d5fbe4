"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { fetchAPI } from "@/strapi"

type User = {
  id: number
  username: string
  email: string
}

type UserContextType = {
  user: User | null
  setUser: (user: User | null) => void
  logout: () => void
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    const token = localStorage.getItem("token")
    if (token) {
      fetchAPI("/users/me", {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      .then(data => {
        setUser(data)
      })
      .catch(() => {
        localStorage.removeItem("token")
      })
    }
  }, [])

  const logout = () => {
    localStorage.removeItem("token")
    setUser(null)
  }

  return (
    <UserContext.Provider value={{ user, setUser, logout }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}