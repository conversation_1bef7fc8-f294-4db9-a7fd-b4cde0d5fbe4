"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp, ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"

import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText?: string
  caption?: string
  width: number | null
  height: number | null
  formats?: StrapiImageFormats
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl?: string
  provider: string
  provider_metadata?: any
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale?: string
  isVideo?: boolean
}

interface AffiliatedAssociation {
  id: number
  documentId: string
  title: string
  description?: any[]
  website?: string
  content?: any[]
  order?: string
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Loading component
function AffiliatedAssociationsLoading() {
  return (
    <div className="min-h-screen">
      <div className="container py-12 space-y-8">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="h-6 bg-muted rounded animate-pulse w-48 mx-auto" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                <div className="h-4 bg-muted rounded animate-pulse w-4/6 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-16">
          {Array(3).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-10 items-start">
                  <div className="space-y-4">
                    <div className="h-8 bg-muted rounded animate-pulse w-3/4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
                      <div className="h-4 bg-muted rounded animate-pulse w-4/6" />
                    </div>
                  </div>
                  <div className="aspect-[16/12] bg-muted rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Main content component
function AffiliatedAssociationsContent() {
  const searchParams = useSearchParams()
  const [associations, setAssociations] = useState<AffiliatedAssociation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedAssociations, setExpandedAssociations] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (associationId: number) => {
    setExpandedAssociations(prev => {
      const newSet = new Set(prev)
      if (newSet.has(associationId)) {
        newSet.delete(associationId)
      } else {
        newSet.add(associationId)
      }
      return newSet
    })
  }

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae'

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/affiliated-associations`)
        url.searchParams.append('populate', '*')
        if (getStrapiLocale) {
          url.searchParams.append('locale', getStrapiLocale)
        }

        console.log('Affiliated Associations - Current language:', currentLang)
        console.log('Affiliated Associations - Strapi locale:', getStrapiLocale)
        console.log('Affiliated Associations - API URL:', url.toString())

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        })

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        console.log('API Response:', data)

        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          setAssociations(data.data)
        } else {
          console.error('Affiliated associations data not found in response:', data)
          setError(t('affiliatedAssociation.noAssociations'))
        }
      } catch (error: any) {
        console.error('Error fetching affiliated associations data:', error)
        setError(t('affiliatedAssociation.error'))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Helper function to render rich text content
  const renderRichText = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('affiliatedAssociation.noContent')}</p>
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children?.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null
    })
  }

  // Helper function to get association images
  const getAssociationImages = (association: AffiliatedAssociation): StrapiImage[] => {
    const images: StrapiImage[] = []
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com'

    // Check if image is an array (multiple images)
    if (Array.isArray(association.image)) {
      images.push(...association.image.map(img => ({
        ...img,
        url: img.url?.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
        isVideo: img.mime?.startsWith('video/') || false
      })))
    } else if (association.image) {
      // Single image
      images.push({
        ...association.image,
        url: association.image.url?.startsWith('http') ? association.image.url : `${strapiUrl}${association.image.url}`,
        isVideo: association.image.mime?.startsWith('video/') || false
      })
    }

    // Also check images field
    if (association.images && Array.isArray(association.images)) {
      images.push(...association.images.map(img => ({
        ...img,
        url: img.url?.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
        isVideo: img.mime?.startsWith('video/') || false
      })))
    }

    return images
  }

  // Helper function to get image URL with fallback
  const getImageUrl = (image: StrapiImage | undefined): string => {
    if (!image) return "/placeholder.svg"

    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com'

    // For videos, return the preview URL if available
    if (image.mime?.startsWith('video/')) {
      return image.previewUrl || "/placeholder.svg"
    }

    // For images, try different format sizes
    if (image.formats?.medium?.url) {
      return image.formats.medium.url.startsWith('http')
        ? image.formats.medium.url
        : `${strapiUrl}${image.formats.medium.url}`
    }
    if (image.formats?.small?.url) {
      return image.formats.small.url.startsWith('http')
        ? image.formats.small.url
        : `${strapiUrl}${image.formats.small.url}`
    }
    if (image.url) {
      return image.url.startsWith('http')
        ? image.url
        : `${strapiUrl}${image.url}`
    }

    return "/placeholder.svg"
  }

  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('affiliatedAssociation.staticTitle')}</h2>
            <p>{t('affiliatedAssociation.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-16">
        {loading ? (
          // Loading skeletons
          Array(3).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-10 items-start">
                  <div className="space-y-4">
                    <div className="h-8 bg-muted rounded animate-pulse w-3/4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
                      <div className="h-4 bg-muted rounded animate-pulse w-4/6" />
                    </div>
                  </div>
                  <div className="aspect-[16/12] bg-muted rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : error ? (
          <Card>
            <CardContent className="p-8">
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
              </div>
            </CardContent>
          </Card>
        ) : associations.length > 0 ? (
          associations.map((association) => {
            const isExpanded = expandedAssociations.has(association.id)
            const images = getAssociationImages(association)

            // Determine if content is long enough to show read more/less
            const hasLongContent = (association.description && association.description.length > 2) ||
                                 (association.content && association.content.length > 2)

            return (
              <Card key={association.id} className="overflow-hidden">
                <CardContent className="p-8">
                  <div className="grid md:grid-cols-2 gap-8 items-start">
                    <div className="space-y-4">
                      <h1 className="text-3xl font-bold mb-4">{association.title}</h1>

                      {/* Description */}
                      {association.description && (
                        <div className={`space-y-4 ${!isExpanded && hasLongContent ? 'line-clamp-3' : ''}`}>
                          {renderRichText(association.description)}
                        </div>
                      )}

                      {/* Content */}
                      {association.content && (
                        <div className={`space-y-4 ${!isExpanded && hasLongContent ? 'line-clamp-3' : ''}`}>
                          {renderRichText(association.content)}
                        </div>
                      )}

                      {/* Website */}
                      {association.website && (
                        <p className="mt-4">
                          <strong>Website:</strong>{" "}
                          <a
                            href={
                              association.website.startsWith("http")
                                ? association.website
                                : `https://${association.website}`
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {association.website}
                          </a>
                        </p>
                      )}

                      {/* Read More/Less Button */}
                      {hasLongContent && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(association.id)}
                          className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                        >
                          {isExpanded ? (
                            <>
                              {t('affiliatedAssociation.readLess')} <ChevronUp className="ml-1 h-3 w-3" />
                            </>
                          ) : (
                            <>
                              {t('affiliatedAssociation.readMore')} <ChevronDown className="ml-1 h-3 w-3" />
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    {/* Image Section */}
                    <div className="relative w-full aspect-[4/3] shadow-md rounded-lg overflow-hidden">
                      {(() => {
                        const mediaList = patchMediaMime(getAssociationImages(association))
                        if (mediaList.length > 1) {
                          return <FullImageSlider images={mediaList} alt={association.title || "Affiliated Association"} interval={3000} />
                        } else if (mediaList.length === 1) {
                          return (
                            <div className="relative aspect-[4/3] w-full min-h-[1px]">
                              {mediaList[0].mime?.startsWith('video/') ? (
                                <video
                                  src={mediaList[0].url}
                                  poster={mediaList[0].previewUrl}
                                  controls
                                  className="w-full h-full object-contain"
                                  playsInline
                                />
                              ) : (
                                <Image
                                  src={getImageUrl(mediaList[0])}
                                  alt={association.title || "Affiliated Association"}
                                  fill
                                  className="object-contain"
                                  sizes="(max-width: 768px) 100vw, 50vw"
                                />
                              )}
                            </div>
                          )
                        } else {
                          return (
                            <div className="flex items-center justify-center aspect-[4/3] bg-gray-100">
                              <p className="text-muted-foreground p-4 text-center">No media available</p>
                            </div>
                          )
                        }
                      })()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        ) : (
          <Card>
            <CardContent className="p-8">
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('affiliatedAssociation.noAssociations')}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

// Component that uses searchParams
function AffiliatedAssociationsPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="affiliatedAssociation.title"
        description="affiliatedAssociation.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<AffiliatedAssociationsLoading />}>
        <AffiliatedAssociationsContent />
      </Suspense>
    </div>
  )
}

// Main export with Suspense wrapper
export default function AffiliatedAssociations() {
  return (
    <Suspense fallback={<AffiliatedAssociationsLoading />}>
      <AffiliatedAssociationsPage />
    </Suspense>
  )
}
