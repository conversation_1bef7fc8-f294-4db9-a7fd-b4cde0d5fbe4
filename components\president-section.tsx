"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import Image from "next/image"

export default function PresidentSection() {
  return (
    <section className="relative overflow-hidden rounded-lg">
      <div className="absolute inset-0">
        <Image src="/placeholder.svg" alt="Building Background" fill className="object-cover opacity-10" />
      </div>
      <div className="relative bg-gradient-to-r from-[#F5F5F5] to-transparent p-8">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
          <h2 className="text-xl font-bold mb-4">會長專欄</h2>
          <h3 className="text-2xl font-bold mb-4">凝聚華商 促進創新</h3>
          <p className="text-gray-600 mb-6 max-w-lg">
            香港中總致力於為新一屆商界領袖培訓，我們將積極推動創新發展，在經濟上繼續發展機遇和合作。
          </p>
          <Button variant="outline" className="bg-[#1E1B4B] text-white hover:bg-[#1E1B4B]/90">
            詳細內容
          </Button>
        </motion.div>
        <div className="absolute right-0 top-0 bottom-0 w-1/3">
          <Image src="/placeholder.svg" alt="President" fill className="object-contain object-right" />
        </div>
      </div>
    </section>
  )
}

