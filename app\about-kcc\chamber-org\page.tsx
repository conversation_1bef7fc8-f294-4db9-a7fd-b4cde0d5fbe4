"use client"

import { useState, useEffect, Suspense, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

interface ImageFormat {
  url: string
  width: number
  height: number
}

interface ImageData {
  id: number
  documentId: string
  name: string
  alternativeText: string | null
  formats: {
    large?: ImageFormat
    medium?: ImageFormat
    small?: ImageFormat
    thumbnail?: ImageFormat
  }
  url: string
  mime?: string
  previewUrl?: string
}

interface ChamberOrgData {
  id: number;
  documentId: string;
  title: string;
  subtitle: string;
  content: {
    type: string;
    children: {
      text: string;
      type: string;
    }[];
  }[];
  order: string;
  image?: ImageData | ImageData[];
  images?: ImageData[];
}

function ChamberOrgContent() {
  const [orgData, setOrgData] = useState<ChamberOrgData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const searchParams = useSearchParams();
  const currentLang = getLanguageFromParams(searchParams);
  const { t } = useTranslation(currentLang);

  // Helper function to get Strapi locale from URL parameter
  const getStrapiLocale = () => {
    const lang = searchParams.get('lang')
    switch (lang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }

  useEffect(() => {
    async function fetchData() {
      try {
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae';

        // Get the current locale
        const locale = getStrapiLocale()

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/chamber-orgs`)
        url.searchParams.append('populate', '*')
        if (locale) {
          url.searchParams.append('locale', locale)
        }

        console.log('Fetching from URL:', url.toString())

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          setOrgData(data.data);
        } else {
          console.error('Chamber organization data not found in response:', data);
          setError(t('chamberOrg.error'));
        }
      } catch (error) {
        console.error('Error fetching chamber org data:', error);
        setError(t('chamberOrg.error'));
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [searchParams]); // Re-run when search params (including lang) change

  // Function to toggle expanded state
  const toggleExpanded = (id: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Function to render rich text content with read more functionality
  const renderContent = (content: any[], itemId: number) => {
    if (!content || !Array.isArray(content)) {
      return <p>{t('chamberOrg.noContent')}</p>;
    }

    const isExpanded = expandedItems.has(itemId);
    const contentText = content.map(block => {
      if (block.type === "paragraph") {
        return block.children.map((child: any) => child.text).join('');
      }
      return '';
    }).join(' ');

    // Show more content when space is available, avoid heavy truncation
    const shouldTruncate = contentText.length > 400;
    const displayText = shouldTruncate && !isExpanded
      ? contentText.substring(0, 400) + '...'
      : contentText;

    return (
      <div className="space-y-4">
        {content.map((block, index) => {
          if (block.type === "paragraph") {
            const blockText = block.children.map((child: any) => child.text).join('');

            // If truncating and this block would exceed the limit, handle it
            if (shouldTruncate && !isExpanded) {
              const currentLength = content.slice(0, index).reduce((acc, b) => {
                if (b.type === "paragraph") {
                  return acc + b.children.map((c: any) => c.text).join('').length;
                }
                return acc;
              }, 0);

              if (currentLength >= 400) {
                return null; // Don't render this block if we're truncating
              }

              if (currentLength + blockText.length > 400) {
                const remainingChars = 400 - currentLength;
                const truncatedText = blockText.substring(0, remainingChars) + '...';
                return (
                  <p key={index} className="mb-4">
                    {truncatedText}
                  </p>
                );
              }
            }

            return (
              <p key={index} className="mb-4">
                {block.children.map((child: any, childIndex: number) => (
                  <span key={childIndex}>{child.text}</span>
                ))}
              </p>
            );
          }
          return null;
        })}

        {shouldTruncate && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => toggleExpanded(itemId)}
            className="mt-4"
          >
            {isExpanded ? t('chamberOrg.readLess') : t('chamberOrg.readMore')}
          </Button>
        )}
      </div>
    );
  };

  // Function to get optimal image URL
  const getOptimalImageUrl = (image: ImageData) => {
    if (image.formats?.large?.url) return image.formats.large.url;
    if (image.formats?.medium?.url) return image.formats.medium.url;
    if (image.formats?.small?.url) return image.formats.small.url;
    return image.url;
  };

  const patchMediaMime = (mediaArr: ImageData[]): ImageData[] =>
    mediaArr.map((item: ImageData) =>
      item.mime
        ? item
        : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
    );

  // Local FullImageSlider for multiple images/videos
  function FullImageSlider({ images, alt, interval = 3000 }: {
    images: ImageData[]
    alt: string
    interval?: number
  }) {
    const [currentIndex, setCurrentIndex] = useState(0)
    const videoRef = useRef<HTMLVideoElement | null>(null)

    // Handlers for arrows
    const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
    const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

    useEffect(() => {
      if (images.length <= 1) return

      const currentMedia = images[currentIndex]
      let timer: NodeJS.Timeout | null = null
      let videoEl: HTMLVideoElement | null = null

      if (currentMedia.mime?.startsWith('video/')) {
        videoEl = videoRef.current
        if (videoEl) {
          const handleEnded = () => {
            goToNext()
          }
          videoEl.addEventListener('ended', handleEnded)
          return () => {
            videoEl && videoEl.removeEventListener('ended', handleEnded)
          }
        }
      } else {
        timer = setInterval(() => {
          goToNext()
        }, interval)
        return () => {
          if (timer) clearInterval(timer)
        }
      }
    }, [images, currentIndex, interval])

    if (images.length === 0) {
      return (
        <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
          <p className="text-muted-foreground">No media available</p>
        </div>
      )
    }

    const currentMedia = images[currentIndex]

    return (
      <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
        {/* Left Arrow */}
        {images.length > 1 && (
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
            onClick={goToPrev}
            aria-label="Previous slide"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>
        )}

        {/* Current Media */}
        <div className="relative w-full h-full flex items-center justify-center">
          {currentMedia.mime?.startsWith('video/') ? (
            <video
              ref={videoRef}
              src={currentMedia.url}
              poster={currentMedia.previewUrl}
              controls
              className="w-full h-full object-contain"
              playsInline
              autoPlay={false}
            />
          ) : (
            <Image
              src={currentMedia.url}
              alt={`${alt} - media ${currentIndex + 1}`}
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          )}
        </div>

        {/* Right Arrow */}
        {images.length > 1 && (
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
            onClick={goToNext}
            aria-label="Next slide"
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        )}

        {/* Dots indicator for multiple media items */}
        {images.length > 1 && (
          <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
            <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
              {images.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentIndex ? "bg-white" : "bg-white/50"
                  } pointer-events-auto`}
                  onClick={() => setCurrentIndex(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title="chamberOrg.title"
        description="chamberOrg.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('chamberOrg.staticTitle')}</h2>
              <p>
                {t('chamberOrg.staticDescription')}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardContent className="p-8">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                {error}
              </div>
            ) : orgData.length > 0 ? (
              <div className="space-y-16">
                {orgData.map((org, index) => (
                  <div key={org.id} className="prose prose-lg max-w-none">
                    {index > 0 && <hr className="my-8" />}
                    <div className="grid md:grid-cols-2 gap-8 items-start">
                      <div className="space-y-4">
                        <h1 className="text-3xl font-bold mb-2">{org.title}</h1>
                        <h2 className="text-xl text-muted-foreground mb-6">{org.subtitle}</h2>
                        {renderContent(org.content, org.id)}
                      </div>
                      <div className="relative aspect-video">
                        {/* Use the exact same pattern as news page for image handling */}
                        {Array.isArray(org.image) && org.image.length > 1 ? (
                          <FullImageSlider
                            images={patchMediaMime(org.image)}
                            alt={org.title || "Chamber Organization"}
                            interval={4000}
                          />
                        ) : org.images && org.images.length > 1 ? (
                          <FullImageSlider
                            images={patchMediaMime(org.images)}
                            alt={org.title || "Chamber Organization"}
                            interval={4000}
                          />
                        ) : Array.isArray(org.image) && org.image.length === 1 ? (
                          org.image[0].mime?.startsWith('video/') ? (
                            <video
                              src={org.image[0].url}
                              poster={org.image[0].previewUrl}
                              controls
                              className="w-full h-full object-contain rounded-lg"
                              playsInline
                            />
                          ) : (
                            <Image
                              src={getOptimalImageUrl(org.image[0])}
                              alt={org.title || "Chamber Organization"}
                              fill
                              className="object-contain rounded-lg"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          )
                        ) : org.images && org.images.length === 1 ? (
                          org.images[0].mime?.startsWith('video/') ? (
                            <video
                              src={org.images[0].url}
                              poster={org.images[0].previewUrl}
                              controls
                              className="w-full h-full object-contain rounded-lg"
                              playsInline
                            />
                          ) : (
                            <Image
                              src={getOptimalImageUrl(org.images[0])}
                              alt={org.title || "Chamber Organization"}
                              fill
                              className="object-contain rounded-lg"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          )
                        ) : (
                          <div className="relative aspect-[4/3]">
                            <Image
                              src={
                                typeof org.image === 'object' && !Array.isArray(org.image) && (org.image as any).url
                                  ? ('formats' in (org.image as any) ? getOptimalImageUrl(org.image as any) : (org.image as any).url)
                                  : "/placeholder.svg"
                              }
                              alt={org.title || "Chamber Organization"}
                              fill
                              className="object-contain rounded-lg"
                              onError={(e) => {
                                console.error('Error loading image');
                                (e.target as HTMLImageElement).src = "/placeholder.svg";
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('chamberOrg.noContent')}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Loading component for Suspense fallback
function ChamberOrgLoading() {
  return (
    <div className="min-h-screen">
      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4 mx-auto" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3 mx-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-8">
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Main component with Suspense boundary
export default function ChamberOrg() {
  return (
    <Suspense fallback={<ChamberOrgLoading />}>
      <ChamberOrgContent />
    </Suspense>
  )
}