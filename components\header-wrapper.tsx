"use client"

import { Suspense } from "react"
import Header from "./header"

function HeaderFallback() {
  return (
    <header>
      {/* Top Bar */}
      <div className="bg-[#1E1B4B] text-white">
        <div className="container">
          <div className="flex justify-end h-8 items-center text-sm">
            <div className="flex items-center gap-2">
              <span>繁</span>
              <span>简</span>
              <span>EN</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="bg-white border-b">
        <div className="container">
          <div className="flex h-20 items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative w-16 h-16 bg-gray-200 rounded"></div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-[#1E1B4B]">KCC</span>
              </div>
            </div>
            <nav className="hidden lg:block">
              <div className="flex items-center gap-6">
                <span>Loading...</span>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </header>
  )
}

export default function HeaderWrapper() {
  return (
    <Suspense fallback={<HeaderFallback />}>
      <Header />
    </Suspense>
  )
}
