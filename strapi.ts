import qs from "qs"

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL || "https://strapibackendproject-3x6s.onrender.com/api"
const STRAPI_TOKEN = process.env.NEXT_PUBLIC_STRAPI_TOKEN

export async function fetchAPI(path: string, urlParamsObject = {}, options = {}) {
  try {
    // Determine if this is an auth request
    const isAuthRequest = path.startsWith('auth/');

    // Merge default and user options
    const mergedOptions = {
      next: { revalidate: 60 },
      headers: {
        "Content-Type": "application/json",
        // Only add Authorization header for non-auth requests
        ...(isAuthRequest ? {} : { Authorization: `Bearer ${STRAPI_TOKEN}` }),
      },
      ...options,
    }

    // Build request URL
    const queryString = qs.stringify(urlParamsObject)
    // Remove any leading slash from path
    const cleanPath = path.startsWith('/') ? path.substring(1) : path
    const requestUrl = `${STRAPI_URL}/${cleanPath}${queryString ? `?${queryString}` : ""}`
    
    console.log('Fetching from URL:', requestUrl)

    // Trigger API call
    const response = await fetch(requestUrl, mergedOptions)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const data = await response.json()
    console.log('API Response:', data)
    return data

  } catch (error) {
    console.error('API Error:', error)
    throw error
  }
}

export async function fetchRentals(params?: any) {
  try {
    console.log('Fetching rentals with params:', params)
    const response = await fetchAPI('rentals', params)
    console.log('Rentals response:', response)
    return response
  } catch (error) {
    console.error('Error fetching rentals:', error)
    throw error
  }
} 