"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"

interface ElderlyCenterData {
  id: number;
  title: string;
  description?: any[];
  services?: any[];
  content?: any[];
  location?: string;
  image?: {
    url: string;
    formats?: {
      large?: { url: string };
      medium?: { url: string };
      small?: { url: string };
    };
  };
}

export default function ElderlyCenter() {
  const [centersData, setCentersData] = useState<ElderlyCenterData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae';

        const response = await fetch(`${strapiUrl}/elderly-centers?populate=*`, {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const centers = data?.data || [];

        if (centers.length > 0) {
          const formattedCenters = centers.map((center: any) => {
            const attributes = center.attributes || center;
            return {
              id: center.id,
              title: attributes.title,
              description: attributes.description,
              services: attributes.services,
              content: attributes.content,
              location: attributes.location,
              image: attributes.image?.formats?.large?.url
                ? { url: attributes.image.formats.large.url }
                : attributes.image?.formats?.medium?.url
                ? { url: attributes.image.formats.medium.url }
                : attributes.image?.url
                ? { url: attributes.image.url }
                : undefined
            };
          });
          setCentersData(formattedCenters);
        } else {
          setError('No data found.');
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const renderContent = (content: any[]) => {
    if (!content || !Array.isArray(content)) return <p>No content available</p>;

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        );
      }
      return null;
    });
  };

  const renderServices = (services: any[]) => {
    if (!services || !Array.isArray(services)) return <p>No services available</p>;

    return (
      <ul className="list-disc pl-5 space-y-2">
        {services.map((item, index) => (
          <li key={index}>
            {item.type === "paragraph" ? item.children.map((child: any) => child.text).join("") : item}
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{ zh: "耆英中心", en: "Elderly Center" }}
        description={{ zh: "了解我們的耆英中心", en: "Learn about our elderly center" }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">老年中心 / Elderly Center</h2>
              <p>
                九龍總商會老年中心致力於為長者提供全面的關懷和服務，
                包括健康檢查、娛樂活動、教育課程和社交活動等。
                我們相信長者是社會寶貴的財富，應獲得尊重和關懷。
              </p>
              <p>
                The Kowloon Chamber of Commerce Elderly Center is dedicated to providing comprehensive care and services for the elderly,
                including health check-ups, entertainment activities, educational courses, and social activities.
                We believe that the elderly are valuable assets to society and deserve respect and care.
              </p>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <Card className="overflow-hidden">
            <CardContent className="p-8">
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </CardContent>
          </Card>
        ) : error ? (
          <Card className="overflow-hidden">
            <CardContent className="p-8">
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
              </div>
            </CardContent>
          </Card>
        ) : centersData.length > 0 ? (
          <div className="space-y-12">
            {centersData.map((center) => (
              <Card key={center.id} className="overflow-hidden">
                <CardContent className="p-8">
                  <div className="prose prose-lg max-w-none">
                    <div className="flex justify-between items-center mb-4">
                      <h1 className="text-3xl font-bold mb-0">{center.title}</h1>
                      {center.location && (
                        <span className="text-gray-600 bg-gray-100 px-3 py-1 rounded-full text-sm">
                          {center.location}
                        </span>
                      )}
                    </div>

                    <div className="grid md:grid-cols-2 gap-8 items-start">
                      <div className="space-y-4">
                        {center.description && renderContent(center.description)}
                        {center.content && renderContent(center.content)}

                        {center.services && (
                          <div className="mt-6">
                            <h3 className="text-xl font-semibold mb-3">Our Services</h3>
                            {renderServices(center.services)}
                          </div>
                        )}
                      </div>

                      {center.image?.url && (
                        <div className="relative aspect-video mt-6 md:mt-0">
                          <Image
                            src={center.image.url}
                            alt={`${center.title} - Elderly Center`}
                            fill
                            className="object-cover rounded-lg"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="overflow-hidden">
            <CardContent className="p-8">
              <div className="text-center py-12 text-muted-foreground">No data available</div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
