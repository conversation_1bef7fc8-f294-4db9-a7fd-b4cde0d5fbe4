"use client"

import { Building2, Globe2, Users } from "lucide-react"
import SingleLanguageFancyCard from "@/components/single-language-fancy-card"
import SingleLanguageSectionHeader from "@/components/single-language-section-header"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams, getTranslation } from "@/lib/translations"

const features = [
  {
    icon: <Building2 className="w-6 h-6" />,
    titleKey: "features.chamberActivities.title" as const,
    descriptionKey: "features.chamberActivities.description" as const,
  },
  {
    icon: <Globe2 className="w-6 h-6" />,
    titleKey: "features.internationalRelations.title" as const,
    descriptionKey: "features.internationalRelations.description" as const,
  },
  {
    icon: <Users className="w-6 h-6" />,
    titleKey: "features.memberServices.title" as const,
    descriptionKey: "features.memberServices.description" as const,
  },
]

export default function ChamberFeatures() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  return (
    <section className="container py-16 md:py-24">
      <SingleLanguageSectionHeader
        title={t('features.title')}
        description={t('features.description')}
        align="center"
      />

      <div className="grid md:grid-cols-3 gap-8">
        {features.map((feature, i) => (
          <SingleLanguageFancyCard
            key={i}
            icon={feature.icon}
            title={t(feature.titleKey)}
            description={t(feature.descriptionKey)}
          />
        ))}
      </div>
    </section>
  )
}
