'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ImageIcon, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchNavyActivities, type NavyActivity } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import ImageSlider from '@/components/image-slider'

export default function SeaScoutsPage() {
  const [activities, setActivities] = useState<NavyActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 100;
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching navy activities (sea scouts)...')
        const activitiesData = await fetchNavyActivities({ populate: '*' })
        console.log('Fetched navy activities:', activitiesData)

        if (activitiesData && activitiesData.length > 0) {
          setActivities(activitiesData) // Get all items
        } else {
          setError('No sea scouts activities data found')
        }
      } catch (err) {
        console.error('Error fetching navy activities:', err)
        setError(err instanceof Error ? err.message : 'An error occurred while fetching data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (error) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title={{
            zh: "海童軍",
            en: "Sea Scouts",
          }}
          description={{
            zh: "了解九龍總商會海童軍的活動和海上訓練項目",
            en: "Learn about KCC Sea Scouts activities and maritime training programs",
          }}
          image="/placeholder.svg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "海童軍",
          en: "Sea Scouts",
        }}
        description={{
          zh: "了解九龍總商會海童軍的活動和海上訓練項目",
          en: "Learn about KCC Sea Scouts activities and maritime training programs",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">海童軍 / Sea Scouts</h2>
              <p>
                九龍總商會海童軍致力於培養青少年的海事技能和領導能力，通過各種海上活動和訓練課程，
                讓參與者學習航海知識、團隊合作和責任感，為未來的海事發展奠定基礎。
              </p>
              <p>
                The KCC Sea Scouts are dedicated to developing maritime skills and leadership abilities in young people through various marine activities and training courses,
                allowing participants to learn navigation knowledge, teamwork, and responsibility, laying the foundation for future maritime development.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Sea Scouts Activities Listing */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Sea Scouts Activities Items */}
              {activities.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">海童軍活動</span> / Sea Scouts Activities
                    </h2>
                    <p className="text-muted-foreground">海童軍的海事訓練和活動 / Sea Scouts maritime training and activities</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {activities.map((activity) => {
                      const isExpanded = expandedCards[activity.id]
                      const showReadMore = shouldShowReadMore(activity.description)

                      return (
                        <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {/* Image Slider for multiple images */}
                          {activity.image && activity.image.length > 0 ? (
                            <ImageSlider
                              images={activity.image.map(img => ({
                                id: img.id,
                                url: img.url,
                                formats: img.formats
                              }))}
                              alt={activity.event_name || "Sea Scouts Activity Image"}
                              interval={3000}
                            />
                          ) : (
                            <div className="relative aspect-video">
                              <Image
                                src="/placeholder.svg"
                                alt={activity.event_name || "Sea Scouts Activity"}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{activity.event_name}</h3>
                            <div className="space-y-2 mb-4">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{activity.location}</span>
                              </div>
                              {activity.organizer && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{activity.organizer}</span>
                                </div>
                              )}
                            </div>
                            <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                              {activity.description}
                            </div>

                            {showReadMore && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(activity.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    Show Less <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    Read More <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    <span className="block">暫無海童軍活動資料</span>
                    <span className="block">No sea scouts activities available at the moment</span>
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}
