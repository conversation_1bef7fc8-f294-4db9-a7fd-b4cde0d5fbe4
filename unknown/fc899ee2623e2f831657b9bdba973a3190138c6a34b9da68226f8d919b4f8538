import { Suspense } from "react"
import NewsContent from "./news-content"

function LoadingFallback() {
  return (
    <div className="min-h-screen">
      {/* Hero Section Skeleton */}
      <div className="relative h-96 bg-gray-200 animate-pulse">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-300 to-gray-200" />
      </div>

      <div className="container py-12">
        <div className="mb-8 p-8 bg-gray-100 rounded-lg animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mx-auto mb-4" />
          <div className="h-4 bg-gray-300 rounded w-2/3 mx-auto" />
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {Array(4).fill(null).map((_, index) => (
            <div key={`skeleton-${index}`} className="bg-white rounded-lg shadow-sm border overflow-hidden">
              <div className="aspect-video bg-gray-200 animate-pulse" />
              <div className="p-6 space-y-4">
                <div className="h-4 bg-gray-200 rounded w-32 animate-pulse" />
                <div className="h-6 bg-gray-200 rounded w-3/4 animate-pulse" />
                <div className="h-16 bg-gray-200 rounded w-full animate-pulse" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function NewsPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <NewsContent />
    </Suspense>
  )
}

