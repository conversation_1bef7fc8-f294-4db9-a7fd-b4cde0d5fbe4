"use client"

import { useEffect, useState } from "react"

type StrapiResponse = {
  data: Array<{
    id: number
    documentId: string
    image: string
    photo: {
      id: number
      documentId: string
      name: string
      formats: {
        small: {
          url: string
          width: number
          height: number
          ext: string
          hash: string
          mime: string
          size: number
        }
      }
      url: string
    }
  }>
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

type Test = {
  id: number
  documentId: string
  image: string
  photo: {
    id: number
    formats: {
      small: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  } | null
}


const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export default function TestPage() {
  const [tests, setTests] = useState<Test[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastFetchTime, setLastFetchTime] = useState<number | null>(null)


  const handleRetry = () => {
    console.log('Retrying fetch...')
    fetchTests()
  }

  const fetchTests = async () => {
    if (lastFetchTime && Date.now() - lastFetchTime < CACHE_DURATION && tests.length > 0) {
      console.log('Using cached data')
      return
    }

    const controller = new AbortController()

    try {
      setLoading(true)
      setError(null)
      const apiUrl = process.env.NEXT_PUBLIC_STRAPI_URL
      const apiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN

      if (!apiToken) {
        throw new Error('API Token is missing. Please check your environment variables.')
      }

      console.log('Fetching from:', `${apiUrl}/tests?populate=*`)

      const response = await fetch(`${apiUrl}/tests?populate=*`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${apiToken}`
        }
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Error response:', errorText)
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const response_data: StrapiResponse = await response.json()
      console.log('API Response:', response_data)

      if (response_data.data && Array.isArray(response_data.data)) {
        // First item debug
        if (response_data.data[0]) {
          console.log('First item example:', {
            photo: response_data.data[0].photo,
            formats: response_data.data[0].photo?.formats,
            smallUrl: response_data.data[0].photo?.formats?.small?.url
          });
        }
const mappedTests: Test[] = response_data.data.map(item => {
  // Log item data for debugging
  console.log('Processing test item:', {
    id: item.id,
    documentId: item.documentId,
    hasPhoto: !!item.photo,
    photoFormats: item.photo?.formats
  });

  // Safely construct the photo object
  let photo = null;
  if (item.photo) {
    try {
      photo = {
        id: item.photo.id,
        formats: {
          small: {
            url: item.photo.formats?.small?.url || '',
            width: item.photo.formats?.small?.width || 800,
            height: item.photo.formats?.small?.height || 600
          }
        },
        url: item.photo.url || ''
      };
    } catch (error) {
      console.error('Error processing photo for item:', item.id, error);
    }
  }

  return {
    id: item.id,
    documentId: item.documentId,
    image: item.image || '',
    photo
  };
});

setTests(mappedTests);
setLastFetchTime(Date.now());
      } else {
        throw new Error('Invalid data format received from API')
      }
    } catch (error: any) {
      console.error('Error:', error)
      setError(error.message || 'Failed to fetch images')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTests()
  }, [])

  if (loading) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4">Loading Images...</h1>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-bold mb-4 text-red-500">Error Loading Images</h1>
        <p className="text-red-600">{error}</p>
        <button
          onClick={handleRetry}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Test Images ({tests.length})</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {tests.map((test, index) => {
          let imageUrl = '';
          
          try {
            if (test.photo?.formats?.small?.url) {
              imageUrl = test.photo.formats.small.url;
              console.log('Image URL constructed:', {
                id: test.id,
                url: imageUrl
              });
            }
          } catch (error) {
            console.error('Error constructing URL for test:', test.id, error);
            
          }

          return (
            <div key={test.id} className="border rounded-lg p-4 shadow-md">
              {imageUrl ? (
                <img
                  src={imageUrl}
                  alt={test.image || 'Test image'}
                  className="w-full h-[300px] object-contain"
                />
              ) : (
                <div className="w-full h-[300px] bg-gray-200 flex items-center justify-center">
                  <p>No image available</p>
                </div>
              )}
              <p className="mt-2 text-sm text-gray-700">ID: {test.documentId}</p>
            </div>
          )
        })}
      </div>
    </div>
  )
}
