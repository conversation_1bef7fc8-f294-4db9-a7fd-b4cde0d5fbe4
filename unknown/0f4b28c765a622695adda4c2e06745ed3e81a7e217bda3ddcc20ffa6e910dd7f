"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function ApiCheck() {
  const [endpoint, setEndpoint] = useState("/i18n/locales")
  const [response, setResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const apiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || "https://strapibackendproject-3x6s.onrender.com/api"
  const token = "5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae"

  const checkEndpoint = async () => {
    setLoading(true)
    setError(null)
    setResponse(null)

    try {
      const url = `${apiUrl}${endpoint}`
      console.log(`Checking endpoint: ${url}`)

      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bear<PERSON> ${token}`
        }
      })

      console.log(`Response status: ${response.status} ${response.statusText}`)

      const data = await response.text()

      try {
        // Try to parse as JSON
        const jsonData = JSON.parse(data)
        setResponse(jsonData)
      } catch (e) {
        // If not JSON, just set as text
        setResponse({ text: data })
      }
    } catch (err: any) {
      console.error("Error checking endpoint:", err)
      setError(err.message || "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Common endpoints to try
  const commonEndpoints = [
    "/i18n/locales",
    "/content-types",
    "/users-permissions/roles",
    "/council-members",
    "/our-chambers",
    "/home",
    "/about-kcc",
    "/history",
    "/event",
    "/contact"
  ]

  return (
    <div className="container py-12">
      <h1 className="text-3xl font-bold mb-8">Strapi API Endpoint Checker</h1>

      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Check API Endpoint</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <div className="flex-grow">
                <Input
                  value={endpoint}
                  onChange={(e) => setEndpoint(e.target.value)}
                  placeholder="Enter API endpoint (e.g., /i18n/locales)"
                />
              </div>
              <Button onClick={checkEndpoint} disabled={loading}>
                {loading ? "Checking..." : "Check"}
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 mb-4">
              {commonEndpoints.map((ep) => (
                <Button
                  key={ep}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setEndpoint(ep)
                    checkEndpoint()
                  }}
                >
                  {ep}
                </Button>
              ))}
            </div>

            {error && (
              <div className="p-4 bg-red-50 text-red-500 rounded-md mb-4">
                {error}
              </div>
            )}

            {response && (
              <div>
                <h3 className="text-lg font-semibold mb-2">Response:</h3>
                <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(response, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Troubleshooting</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>If you're seeing 404 errors, it means the content type doesn't exist or has no published content.</p>

            <h3 className="text-lg font-semibold">Steps to fix:</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Log in to your Strapi admin panel at <a href="https://strapibackendproject-3x6s.onrender.com/admin" target="_blank" className="text-blue-500 hover:underline">https://strapibackendproject-3x6s.onrender.com/admin</a></li>
              <li>Check if the content type exists (Content-Type Builder)</li>
              <li>If it exists, create and publish content for that content type (Content Manager)</li>
              <li>Make sure the Public role has permission to access the content type (Settings → Roles → Public)</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}