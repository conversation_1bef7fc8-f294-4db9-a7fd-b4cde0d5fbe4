"use client"

import { useEffect } from "react"
import { useUser } from "@/contexts/user-context"
import { useRouter } from "next/navigation"
import { UserEvents } from "@/components/user-events"

export default function DashboardPage() {
  const { user } = useUser()
  const router = useRouter()

  useEffect(() => {
    if (!user) {
      router.push("/login")
    }
  }, [user, router])

  if (!user) {
    return null
  }

  return (
    <div className="container py-12">
      <h1 className="text-3xl font-bold mb-6">
        <span className="block mb-1">歡迎來到您的儀表板</span>
        <span className="text-xl text-gray-600">Welcome to your Dashboard</span>
      </h1>

      <div className="space-y-8">
        {/* Profile Information */}
        <div className="bg-white shadow-md rounded-lg p-8 border border-gray-100">
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-4">
                <span className="block">個人資料</span>
                <span className="text-base text-gray-600">Profile Information</span>
              </h2>

              <div className="mt-4 space-y-4 pl-2">
                <p className="flex flex-col md:flex-row md:items-center gap-1 md:gap-3">
                  <span className="font-medium text-gray-700">用戶名稱 / Username:</span>
                  <span className="text-gray-900">{user.username}</span>
                </p>
                <p className="flex flex-col md:flex-row md:items-center gap-1 md:gap-3">
                  <span className="font-medium text-gray-700">電子郵件 / Email:</span>
                  <span className="text-gray-900">{user.email}</span>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* User Events */}
        <UserEvents />
      </div>
    </div>
  )
}