"use client"

import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { buildUrlWithCurrentLang } from "@/lib/translations"
import { ReactNode } from "react"

interface LanguageLinkProps {
  href: string
  children: ReactNode
  className?: string
  onClick?: () => void
  [key: string]: any // Allow other props to be passed through
}

/**
 * Language-aware Link component that preserves the current language parameter
 * when navigating to different pages
 */
export default function LanguageLink({ href, children, className, onClick, ...props }: LanguageLinkProps) {
  const searchParams = useSearchParams()
  
  // Build URL with current language parameter
  const urlWithLang = buildUrlWithCurrentLang(href, searchParams)
  
  return (
    <Link 
      href={urlWithLang} 
      className={className} 
      onClick={onClick}
      {...props}
    >
      {children}
    </Link>
  )
}
