"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchCommunityCares } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
// Badge import removed as it's no longer used
// import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, Heart } from "lucide-react"

interface CommunityCare {
  id: number
  documentId: string
  title: string
  date?: string
  location?: string
  participants?: string
  beneficiaries?: string
  description?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  image?: {
    id: number
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
}

export default function CommunityCareEventsPage() {
  const [communityEvents, setCommunityEvents] = useState<CommunityCare[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    const loadCommunityEvents = async () => {
      try {
        setLoading(true)
        const response = await fetchCommunityCares({
          populate: "*"
        })

        if (response && response.data) {
          // Log the community care data to debug the structure
          console.log("Community care data:", JSON.stringify(response.data, null, 2));

          // Extract community care events from the response
          const events = Array.isArray(response.data) ? response.data : [response.data];

          if (events.length > 0) {
            setCommunityEvents(events);
            setDebugInfo(`Found ${events.length} community care events.`);
          } else {
            // If no events are found, use fallback data
            setCommunityEvents(fallbackEvents);
            setDebugInfo("No community care events found in API response. Using fallback data.");
          }
        } else {
          // If response is empty, use fallback data
          setCommunityEvents(fallbackEvents);
          setDebugInfo("Empty API response. Using fallback data.");
        }

        setLoading(false)
      } catch (err) {
        console.error("Error loading community care events:", err)
        setError("Failed to load community care events. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setCommunityEvents(fallbackEvents)
        setDebugInfo(`API error: ${err}. Using fallback data.`);
      }
    }

    loadCommunityEvents()
  }, [])

  // Helper function to get image URL
  const getImageUrl = (event: CommunityCare) => {
    if (!event.image) return "/placeholder.svg";
    return event.image.formats?.medium?.url || event.image.formats?.small?.url || event.image.url || "/placeholder.svg";
  }

  // Helper function to get image alt text
  const getImageAlt = (event: CommunityCare) => {
    return event.image?.alternativeText || event.title || "Community Care Event";
  }

  // Helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      return content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      });
    }

    return null;
  };

  // Fallback data in case the API fails
  const fallbackEvents: CommunityCare[] = [
    {
      id: 1,
      documentId: "fallback-1",
      title: "長者關懷探訪活動",
      date: "2023-12-20",
      location: "九龍區多個長者中心",
      participants: "九龍總商會義工團隊",
      beneficiaries: "獨居長者",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會組織義工團隊探訪九龍區多個長者中心，為獨居長者送上關懷和節日祝福。活動中，義工們與長者們一起進行遊戲、表演和茶聚，並送上實用的生活用品和食品。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-12-20T00:00:00.000Z",
      updatedAt: "2023-12-20T00:00:00.000Z",
      publishedAt: "2023-12-20T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 2,
      documentId: "fallback-2",
      title: "社區清潔日",
      date: "2023-11-05",
      location: "九龍灣海濱公園",
      participants: "九龍總商會會員及家屬",
      beneficiaries: "社區居民",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會組織會員及其家屬參與社區清潔日活動，共同清理九龍灣海濱公園的垃圾，美化環境。活動旨在提高公眾的環保意識，同時促進會員之間的交流和合作。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-11-05T00:00:00.000Z",
      updatedAt: "2023-11-05T00:00:00.000Z",
      publishedAt: "2023-11-05T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 3,
      documentId: "fallback-3",
      title: "兒童教育支援計劃",
      date: "2023-09-15",
      location: "九龍多個基層家庭",
      participants: "九龍總商會教育委員會成員",
      beneficiaries: "基層家庭兒童",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會推出兒童教育支援計劃，為九龍區基層家庭的兒童提供學習用品和課後輔導服務。計劃旨在幫助這些兒童獲得更好的教育機會，提高他們的學習興趣和能力。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-09-15T00:00:00.000Z",
      updatedAt: "2023-09-15T00:00:00.000Z",
      publishedAt: "2023-09-15T00:00:00.000Z",
      locale: "zh"
    }
  ]

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "關心社區",
          en: "Community Care",
        }}
        description={{
          zh: "九龍總商會的社區關懷活動",
          en: "Community care activities of the Kowloon Chamber of Commerce",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">關心社區 / Community Care</h2>
              <p>
                九龍總商會積極參與社區服務，關心弱勢群體，回饋社會。
                我們定期組織各類社區關懷活動，包括長者探訪、兒童教育支援、
                環保活動等，為建設和諧社區貢獻力量。
              </p>
              <p>
                The Kowloon Chamber of Commerce actively participates in community services,
                cares for vulnerable groups, and gives back to society.
                We regularly organize various community care activities, including elderly visits,
                children's education support, environmental protection activities, etc.,
                contributing to the construction of a harmonious community.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
            <p>{error}</p>
            {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
          </div>
        )}

        {loading ? (
          // Loading skeleton
          <div className="space-y-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-3 gap-0">
                    <Skeleton className="h-64 w-full" />
                    <div className="md:col-span-2 p-6 md:p-8">
                      <Skeleton className="h-8 w-3/4 mb-3" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-2/3 mb-6" />

                      <div className="grid sm:grid-cols-2 gap-4 mb-6">
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-full sm:col-span-2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-8">
            {communityEvents.length > 0 ? (
              communityEvents.map((event) => (
                <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-all">
                  <CardContent className="p-0">
                    <div className="grid md:grid-cols-3 gap-0">
                      <div className="relative aspect-video md:aspect-square">
                        <Image
                          src={getImageUrl(event)}
                          alt={getImageAlt(event)}
                          fill
                          className="object-cover"
                          sizes="(min-width: 768px) 33vw, 100vw"
                        />
                        {/* Badge removed as requested */}
                      </div>
                      <div className="md:col-span-2 p-6 md:p-8">
                        <h3 className="text-2xl font-bold mb-3">{event.title}</h3>
                        <div className="text-muted-foreground mb-6">
                          {renderStructuredContent(event.description)}
                        </div>

                        <div className="grid sm:grid-cols-2 gap-4 mb-6">
                          {event.date && (
                            <div className="flex items-center gap-2">
                              <Calendar className="h-5 w-5 text-primary" />
                              <span>{event.date}</span>
                            </div>
                          )}
                          {event.location && (
                            <div className="flex items-center gap-2">
                              <MapPin className="h-5 w-5 text-primary" />
                              <span>{event.location}</span>
                            </div>
                          )}
                          {event.participants && (
                            <div className="flex items-center gap-2">
                              <Users className="h-5 w-5 text-primary" />
                              <span>{event.participants}</span>
                            </div>
                          )}
                          {event.beneficiaries && (
                            <div className="flex items-center gap-2">
                              <Heart className="h-5 w-5 text-primary" />
                              <span>{event.beneficiaries}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <p className="text-xl text-muted-foreground">暫無社區關懷活動資料 / No community care events available</p>
                {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}