import { Metadata } from "next"
import PageHeader from "@/components/page-header"
import Image from "next/image"

export const metadata: Metadata = {
  title: "出租單位 | Rental Units - 九龍總商會",
  description: "Information about rental units available from the Kowloon Chamber of Commerce",
}

export default function RentalUnitsPage() {
  return (
    <main className="container py-8">
      <PageHeader
        title={{ zh: "出租單位", en: "Rental Units" }}
        breadcrumbs={[
          { title: { zh: "主頁", en: "Home" }, href: "/" },
          { title: { zh: "聯絡我們", en: "Contact Us" }, href: "/contact" },
          { title: { zh: "出租單位", en: "Rental Units" }, href: "/contact/rental-units" },
        ]}
      />
      
      <div className="prose max-w-none mt-8">
        <h2>出租單位 / Rental Units</h2>
        <p>
          九龍總商會提供多個商業及辦公單位出租，適合不同規模的企業及組織。以下是我們目前可供出租的單位資料。
        </p>
        <p>
          The Kowloon Chamber of Commerce offers various commercial and office units for rent, suitable for businesses and organizations of different sizes. Below is information about our currently available rental units.
        </p>
        
        <div className="grid md:grid-cols-2 gap-8 mt-8">
          {/* Sample rental unit */}
          <div className="border rounded-lg overflow-hidden">
            <div className="relative h-48 w-full bg-gray-200">
              {/* Placeholder for unit image */}
              <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                單位照片 / Unit Photo
              </div>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-bold mb-2">辦公單位 A / Office Unit A</h3>
              <ul className="space-y-2">
                <li><strong>面積 / Area:</strong> 500 平方呎 / 500 sq ft</li>
                <li><strong>位置 / Location:</strong> 九龍尖沙咀彌敦道 80 號 5 樓 / 5/F, 80 Nathan Road, TST</li>
                <li><strong>租金 / Rent:</strong> HK$25,000 / 月 (month)</li>
                <li><strong>設施 / Facilities:</strong> 空調、寬頻、24小時保安 / AC, Broadband, 24hr Security</li>
              </ul>
              <button className="mt-4 bg-[#1E1B4B] text-white px-4 py-2 rounded hover:bg-[#1E1B4B]/90">
                查詢詳情 / Inquire
              </button>
            </div>
          </div>
          
          {/* Sample rental unit */}
          <div className="border rounded-lg overflow-hidden">
            <div className="relative h-48 w-full bg-gray-200">
              {/* Placeholder for unit image */}
              <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                單位照片 / Unit Photo
              </div>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-bold mb-2">商業單位 B / Commercial Unit B</h3>
              <ul className="space-y-2">
                <li><strong>面積 / Area:</strong> 800 平方呎 / 800 sq ft</li>
                <li><strong>位置 / Location:</strong> 九龍尖沙咀彌敦道 80 號地下 / G/F, 80 Nathan Road, TST</li>
                <li><strong>租金 / Rent:</strong> HK$60,000 / 月 (month)</li>
                <li><strong>設施 / Facilities:</strong> 空調、寬頻、獨立入口 / AC, Broadband, Separate Entrance</li>
              </ul>
              <button className="mt-4 bg-[#1E1B4B] text-white px-4 py-2 rounded hover:bg-[#1E1B4B]/90">
                查詢詳情 / Inquire
              </button>
            </div>
          </div>
        </div>
        
        <div className="mt-8 bg-gray-50 p-6 rounded-lg">
          <h3>租賃查詢 / Rental Inquiries</h3>
          <p>
            如欲了解更多關於我們出租單位的資料，請聯絡我們的物業管理部門：<br />
            For more information about our rental units, please contact our property management department:
          </p>
          <p>
            <strong>電話 / Phone:</strong> +852 1234 5678<br />
            <strong>電郵 / Email:</strong> <EMAIL>
          </p>
        </div>
      </div>
    </main>
  )
}
