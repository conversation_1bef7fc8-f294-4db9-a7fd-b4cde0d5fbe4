"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchArchives } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"

interface Archive {
  id: number
  documentId: string
  title: string
  description?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  type?: string
  year?: string
  content?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>
  category?: string
  order?: number
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: {
    id: number
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }
  seo?: any
  localizations?: any[]
  media?: {
    url?: string
  }
}

interface ArchiveType {
  id: string
  label: string
  labelEn: string
  archives: Archive[]
}

export default function ArchivesPage() {
  const [activeTab, setActiveTab] = useState("annual-reports")
  const [archiveTypes, setArchiveTypes] = useState<ArchiveType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    const loadArchives = async () => {
      try {
        setLoading(true)
        const response = await fetchArchives({
          populate: "*"
        })

        if (response && response.data && Array.isArray(response.data)) {
          // Log the first archive to debug the structure
          if (response.data.length > 0) {
            console.log("First archive data:", JSON.stringify(response.data[0], null, 2));
          }

          // Process the archives data
          const archivesData = response.data as Archive[];
          const groupedArchives: Record<string, Archive[]> = {}

          // Group archives by type
          archivesData.forEach(archive => {
            const type = archive.type || "Other"
            if (!groupedArchives[type]) {
              groupedArchives[type] = []
            }
            groupedArchives[type].push(archive)
          })

          // Sort archives by year (newest first)
          Object.keys(groupedArchives).forEach(type => {
            groupedArchives[type].sort((a, b) => {
              if (!a.year && !b.year) return 0
              if (!a.year) return 1
              if (!b.year) return -1
              return b.year.localeCompare(a.year)
            })
          })

          // Convert to array format for tabs
          const types: ArchiveType[] = [
            {
              id: "photos",
              label: "歷史照片",
              labelEn: "Historical Photos",
              archives: groupedArchives["Photos"] || []
            },
            {
              id: "documents",
              label: "歷史文件",
              labelEn: "Historical Documents",
              archives: groupedArchives["Documents"] || []
            },
            {
              id: "publications",
              label: "出版物",
              labelEn: "Publications",
              archives: groupedArchives["Publications"] || []
            },
            {
              id: "other",
              label: "", // Empty label to hide the category name
              labelEn: "", // Empty label in English too
              archives: groupedArchives["Other"] || []
            }
          ].filter(type => type.archives.length > 0)

          setArchiveTypes(types)

          // Set active tab to first type if available
          if (types.length > 0) {
            setActiveTab(types[0].id)
          }

          // Count total archives for debug info
          const totalArchives = types.reduce((total, type) => total + type.archives.length, 0);
          setDebugInfo(`Found ${totalArchives} archives across ${types.length} categories.`);
        }
        setLoading(false)
      } catch (err) {
        console.error("Error loading archives:", err)
        setError("Failed to load archives. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setArchiveTypes(fallbackArchiveTypes)
      }
    }

    loadArchives()
  }, [])

  // Update the fallback data to match the new description format
  const fallbackArchiveTypes: ArchiveType[] = [
    {
      id: "photos",
      label: "歷史照片",
      labelEn: "Historical Photos",
      archives: [
        {
          id: 1,
          documentId: "fallback-1",
          title: "1950年代商會活動照片",
          description: [
            {
              type: "paragraph",
              children: [
                {
                  text: "1950年代九龍總商會舉辦的各類活動照片集，記錄了商會早期的重要活動和歷史時刻。",
                  type: "text"
                }
              ]
            }
          ],
          year: "1950",
          type: "Photos",
          media: {
            url: "/placeholder.svg"
          },
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        },
        {
          id: 2,
          documentId: "fallback-2",
          title: "1970年代商會大樓落成典禮",
          description: [
            {
              type: "paragraph",
              children: [
                {
                  text: "1970年代九龍總商會大樓落成典禮照片集，展示了商會發展的重要里程碑。",
                  type: "text"
                }
              ]
            }
          ],
          year: "1970",
          type: "Photos",
          media: {
            url: "/placeholder.svg"
          },
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        }
      ]
    },
    {
      id: "documents",
      label: "歷史文件",
      labelEn: "Historical Documents",
      archives: [
        {
          id: 3,
          documentId: "fallback-3",
          title: "九龍總商會創會章程",
          description: [
            {
              type: "paragraph",
              children: [
                {
                  text: "1945年九龍總商會創會章程原件，記載了商會成立的宗旨和初期組織架構。",
                  type: "text"
                }
              ]
            }
          ],
          year: "1945",
          type: "Documents",
          media: {
            url: "/placeholder.svg"
          },
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        }
      ]
    },
    {
      id: "publications",
      label: "出版物",
      labelEn: "Publications",
      archives: [
        {
          id: 4,
          documentId: "fallback-4",
          title: "九龍總商會五十週年紀念特刊",
          description: [
            {
              type: "paragraph",
              children: [
                {
                  text: "1995年出版的九龍總商會五十週年紀念特刊，回顧了商會半世紀的發展歷程。",
                  type: "text"
                }
              ]
            }
          ],
          year: "1995",
          type: "Publications",
          media: {
            url: "/placeholder.svg"
          },
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        }
      ]
    }
  ]

  // Helper function to get image URL
  const getImageUrl = (archive: Archive) => {
    if (archive.image?.formats?.medium?.url) return archive.image.formats.medium.url;
    if (archive.image?.url) return archive.image.url;
    if (archive.media?.url) return archive.media.url;
    return "/placeholder.svg";
  }

  // Helper function to get image alt text
  const getImageAlt = (archive: Archive) => {
    return archive.image?.alternativeText || archive.title || "Archive";
  }

  // Add a helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      return content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      });
    }

    return null;
  };

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "歷史檔案",
          en: "Archives",
        }}
        description={{
          zh: "九龍總商會的歷史檔案和文獻",
          en: "Historical archives and documents of the Kowloon Chamber of Commerce",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">歷史檔案 / Archives</h2>
              <p>
                九龍總商會的歷史檔案收藏了自1945年成立以來的重要文獻、照片和出版物，
                包括年度報告、會訊、歷史文獻和照片集等。這些檔案不僅記錄了商會的發展歷程，
                也反映了香港商業社會的變遷。我們將這些珍貴的歷史資料數碼化並向公眾開放，
                希望能為研究香港商業歷史的學者和對商會歷史感興趣的公眾提供參考。
              </p>
              <p>
                The historical archives of the Kowloon Chamber of Commerce collect important documents,
                photographs, and publications since its establishment in 1945, including annual reports,
                newsletters, historical documents, and photo collections. These archives not only record
                the development of the chamber but also reflect the changes in Hong Kong's business society.
                We have digitized these precious historical materials and made them available to the public,
                hoping to provide references for scholars researching Hong Kong's business history and the
                public interested in the chamber's history.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
            <p>{error}</p>
            {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
          </div>
        )}

        {loading ? (
          // Loading skeleton
          <div className="space-y-8">
            <Skeleton className="h-12 w-full max-w-2xl mx-auto" />
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <CardContent className="p-0">
                    <Skeleton className="h-48 w-full" />
                    <div className="p-6">
                      <Skeleton className="h-6 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-1/2 mb-4" />
                      <Skeleton className="h-16 w-full mb-4" />
                      <div className="flex justify-between">
                        <Skeleton className="h-10 w-24" />
                        <Skeleton className="h-10 w-24" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            <TabsList className="w-full max-w-2xl mx-auto h-auto flex flex-wrap gap-2 bg-transparent p-0">
              {archiveTypes.map((type) => (
                type.label ? (
                  <TabsTrigger
                    key={type.id}
                    value={type.id}
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                  >
                    {type.label}
                  </TabsTrigger>
                ) : null
              ))}
            </TabsList>

            {archiveTypes.map((type) => (
              <TabsContent key={type.id} value={type.id} className="space-y-6">
                {type.archives.length > 0 ? (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {type.archives.map((archive) => (
                      <Card key={archive.id} className="overflow-hidden flex flex-col">
                        <div className="relative aspect-[4/3]">
                          <Image
                            src={getImageUrl(archive)}
                            alt={getImageAlt(archive)}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          />
                        </div>
                        <CardContent className="flex-1 p-6">
                          <div className="mb-2">
                            <h3 className="text-lg font-bold">{archive.title}</h3>
                          </div>
                          <div className="text-sm text-muted-foreground mb-4">
                            {renderStructuredContent(archive.description)}
                          </div>
                          <div className="flex justify-between items-center mt-auto">
                            <Badge variant="outline">{archive.year}</Badge>
                            <Button size="sm" variant="outline" asChild>
                              <a href={getImageUrl(archive)} target="_blank" rel="noopener noreferrer">
                                查看 / View
                              </a>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card className="p-6 text-center">
                    <p className="text-muted-foreground">No archives available in this category</p>
                    {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
                  </Card>
                )}
              </TabsContent>
            ))}
          </Tabs>
        )}
      </div>
    </div>
  )
}