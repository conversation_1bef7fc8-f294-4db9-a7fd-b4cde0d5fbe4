"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import LoadingLogo from "./loading-logo"

const slides = [
  {
    image:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/slideshow-102-bd2d1998c91d.jpg-7z6bAuVEnipJgKZJTOJv4lxGyB5jb6.jpeg",
    alt: "九龍第二海童軍旅",
    title: "九龍第二海童軍旅 招募幼童軍成員",
  },
  {
    image:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/slideshow-20-8de0c04fbe6a.jpg-Xlgexc8FxZPymxQzoJ9Zw4TQLOTXnI.jpeg",
    alt: "Business Meeting",
    title: "商會代表團訪問交流",
  },
  {
    image:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/slideshow-102-bd2d1998c91d%20(1).jpg-6jSeaQtdLjTjmWaS9VEuFzplVePUpO.jpeg",
    alt: "九龍第二海童軍旅",
    title: "九龍第二海童軍旅 招募活動",
  },
]

export default function BannerCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((current) => (current === slides.length - 1 ? 0 : current + 1))
    }, 5000)
    return () => clearInterval(timer)
  }, [])

  const prevSlide = () => {
    setCurrentSlide((current) => (current === 0 ? slides.length - 1 : current - 1))
  }

  const nextSlide = () => {
    setCurrentSlide((current) => (current === slides.length - 1 ? 0 : current + 1))
  }

  return (
    <div className="relative h-[60vh] bg-[#1E1B4B] overflow-hidden">
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#1E1B4B]">
          <LoadingLogo />
        </div>
      )}

      {/* Slides */}
      {slides.map((slide, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-opacity duration-1000 ${
            index === currentSlide ? "opacity-100" : "opacity-0"
          }`}
        >
          <Image
            src={slide.image || "/placeholder.svg"}
            alt={slide.alt}
            fill
            className="object-cover"
            priority={index === 0}
            onLoadingComplete={() => setIsLoading(false)}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-[#1E1B4B]/50 to-[#1E1B4B]/80" />
          <div className="absolute inset-0 flex items-center justify-center">
            <h1 className="text-4xl md:text-5xl text-white text-center font-bold max-w-4xl px-4">{slide.title}</h1>
          </div>
        </div>
      ))}

      {/* Navigation Buttons */}
      <div className="absolute inset-0 flex items-center justify-between p-4">
        <Button
          variant="ghost"
          size="icon"
          className="h-12 w-12 rounded-full bg-white/10 hover:bg-white/20 text-white"
          onClick={prevSlide}
        >
          <ChevronLeft className="h-6 w-6" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-12 w-12 rounded-full bg-white/10 hover:bg-white/20 text-white"
          onClick={nextSlide}
        >
          <ChevronRight className="h-6 w-6" />
        </Button>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            className={`h-2 w-2 rounded-full transition-all ${index === currentSlide ? "bg-white w-8" : "bg-white/50"}`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  )
}

