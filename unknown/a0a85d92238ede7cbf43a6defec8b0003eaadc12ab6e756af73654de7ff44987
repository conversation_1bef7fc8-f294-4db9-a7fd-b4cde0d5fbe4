"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import AnimatedHero from "@/components/animated-hero"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function EventsPage() {
  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "活動項目",
          en: "Events",
        }}
        description={{
          zh: "九龍總商會舉辦的各類活動",
          en: "Various events organized by the Kowloon Chamber of Commerce",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none">
              <h2>活動項目 / Events</h2>
              <p>
                九龍總商會積極舉辦各類活動，包括商業交流、社區服務、學術研討等，
                旨在促進商界交流、服務社會、推動學術發展。我們的活動涵蓋多個領域，
                為會員和社會各界提供豐富多彩的參與機會。
              </p>
              <p>
                The Kowloon Chamber of Commerce actively organizes various events, 
                including business exchanges, community services, academic seminars, etc., 
                aiming to promote business exchanges, serve society, and promote academic development. 
                Our events cover multiple fields, providing rich and colorful participation 
                opportunities for members and all sectors of society.
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src="/placeholder.svg"
                alt="Upcoming Events"
                fill
                className="object-cover"
              />
            </div>
            <CardHeader>
              <CardTitle>活動預告 / Upcoming Events</CardTitle>
              <CardDescription>
                即將舉行的活動
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                了解九龍總商會即將舉辦的各類活動，包括商業論壇、研討會、交流會等。
              </p>
              <Button asChild>
                <Link href="/events/upcoming">
                  查看更多 <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src="/placeholder.svg"
                alt="KCC Elite Development Programme"
                fill
                className="object-cover"
              />
            </div>
            <CardHeader>
              <CardTitle>九龍總商會新世紀精英培訓計劃</CardTitle>
              <CardDescription>
                KCC Elite Development Programme
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                為新世紀培養商界精英的計劃，提供全面的培訓和發展機會。
              </p>
              <Button asChild>
                <Link href="/events/elite-programme">
                  查看更多 <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src="/placeholder.svg"
                alt="Reception Guests"
                fill
                className="object-cover"
              />
            </div>
            <CardHeader>
              <CardTitle>接待賓客 / Reception Guests</CardTitle>
              <CardDescription>
                商會接待的重要賓客
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                九龍總商會接待來自各界的重要賓客，促進交流與合作。
              </p>
              <Button asChild>
                <Link href="/events/reception">
                  查看更多 <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src="/placeholder.svg"
                alt="Exchange Visits"
                fill
                className="object-cover"
              />
            </div>
            <CardHeader>
              <CardTitle>外訪交流 / Exchange Visits</CardTitle>
              <CardDescription>
                商會組織的外訪交流活動
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                九龍總商會組織會員前往各地進行商務考察和交流，拓展商機。
              </p>
              <Button asChild>
                <Link href="/events/exchange-visits">
                  查看更多 <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src="/placeholder.svg"
                alt="Community Care"
                fill
                className="object-cover"
              />
            </div>
            <CardHeader>
              <CardTitle>關心社區 / Community Care</CardTitle>
              <CardDescription>
                商會的社區關懷活動
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                九龍總商會積極參與社區服務，關心弱勢群體，回饋社會。
              </p>
              <Button asChild>
                <Link href="/events/community-care">
                  查看更多 <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src="/placeholder.svg"
                alt="Academic Research"
                fill
                className="object-cover"
              />
            </div>
            <CardHeader>
              <CardTitle>學術研討 / Academic Research</CardTitle>
              <CardDescription>
                商會舉辦的學術研討會
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                九龍總商會舉辦各類學術研討會，探討商業發展趨勢和學術前沿。
              </p>
              <Button asChild>
                <Link href="/events/academic-research">
                  查看更多 <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src="/placeholder.svg"
                alt="Chinese Medicine Development"
                fill
                className="object-cover"
              />
            </div>
            <CardHeader>
              <CardTitle>中醫中藥發展資訊</CardTitle>
              <CardDescription>
                Chinese Medicine Development Information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                提供中醫中藥發展的最新資訊，促進中醫藥產業發展。
              </p>
              <Button asChild>
                <Link href="/events/chinese-medicine">
                  查看更多 <ChevronRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

