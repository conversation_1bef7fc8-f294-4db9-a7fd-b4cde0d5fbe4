"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronRight, Home, Building2, Calendar, BookOpen, Phone, UserPlus, History, Newspaper } from "lucide-react"
import Link from "next/link"
import { routes } from "@/app/routes"

export default function SiteMap() {
  const [selectedSection, setSelectedSection] = useState("all")

  // Static site structure data - Following exact header navigation structure
  const siteStructure = [
    {
      title: { en: "Home", zh: "主頁" },
      path: "/",
      icon: <Home className="w-5 h-5" />,
      category: "main",
      items: [
        {
          title: { en: "Latest News", zh: "最新消息" },
          path: "/news",
          description: "Stay updated with KCC's latest announcements and events",
        },
        {
          title: { en: "What is KCC", zh: "商會簡介" },
          path: "/about-kcc/introduction",
          description: "Learn about our organization and mission",
        },
        {
          title: { en: "Management Team", zh: "管理團隊" },
          path: "/about-kcc/management",
          description: "Meet our leadership team",
        },
      ],
    },
    {
      title: { en: "About KCC", zh: "關於本會" },
      path: "/about-kcc",
      icon: <Building2 className="w-5 h-5" />,
      category: "about",
      items: [
        {
          title: { en: "Our Chamber", zh: "商會簡介" },
          path: "/about-kcc/our-chamber",
          description: "Learn about our organization and mission",
        },
        {
          title: { en: "Chamber Org", zh: "商會架構" },
          path: "/about-kcc/chamber-org",
          description: "Understanding our organizational framework",
        },
        {
          title: { en: "Council Member + Past Councils", zh: "理監事會名單+歷屆理監事明表" },
          path: "#",
          description: "Board members and historical council information",
          type: "split",
          splitItems: [
            {
              title: { en: "Council Member", zh: "理監事會名單" },
              path: "/about-kcc/council-member",
              description: "Current board members",
            },
            {
              title: { en: "Past Councils", zh: "歷屆理監事明表" },
              path: "/history/councilors",
              description: "Historical list of council members",
            },
          ],
        },
        {
          title: { en: "Affiliated Association", zh: "屬會名單" },
          path: "/about-kcc/affiliated-associations",
          description: "Our affiliated associations and partners",
        },
        {
          title: { en: "KCC Development + Milestones", zh: "龍總發展史簡+本會發展里程" },
          path: "#",
          description: "Our development history and key milestones",
          type: "split",
          splitItems: [
            {
              title: { en: "KCC Development", zh: "龍總發展史簡" },
              path: "/about-kcc/development-history",
              description: "KCC development history",
            },
            {
              title: { en: "Our Milestones", zh: "本會發展里程" },
              path: "/history/milestones",
              description: "Key milestones in our journey",
            },
          ],
        },
      ],
    },
    {
      title: { en: "Events", zh: "活動項目" },
      path: "/events",
      icon: <Calendar className="w-5 h-5" />,
      category: "events",
      items: [
        {
          title: { en: "Chamber Activities", zh: "商會活動先簡介" },
          path: "#",
          description: "Main chamber activities and programs",
          type: "grid",
          layout: "2x2",
          gridItems: [
            {
              title: { en: "New Era", zh: "新紀元" },
              path: "/events/new-era",
              description: "New era initiatives",
            },
            {
              title: { en: "Elderly Activities", zh: "樂齡" },
              path: "/events/elderly-activities",
              description: "Activities for senior members",
            },
            {
              title: { en: "Youth Festival", zh: "青年節" },
              path: "/events/youth-festival",
              description: "Youth development programs",
            },
            {
              title: { en: "Forums", zh: "論壇" },
              path: "/events/forums",
              description: "Business and community forums",
            },
          ],
        },
        {
          title: { en: "Reception + Monthly Meetings", zh: "接待禮賓+月會" },
          path: "#",
          description: "Reception events and monthly meetings",
          type: "split",
          splitItems: [
            {
              title: { en: "Reception", zh: "接待禮賓" },
              path: "/events/reception",
              description: "Reception and protocol events",
            },
            {
              title: { en: "Monthly Meetings", zh: "月會" },
              path: "/events/monthly-meetings",
              description: "Regular monthly meetings",
            },
          ],
        },
        {
          title: { en: "Exchange Visits", zh: "外訪交流" },
          path: "/events/exchange-visits",
          description: "Exchange visits and international cooperation",
        },
        {
          title: { en: "Affiliated Activities", zh: "屬會活動" },
          path: "/events/affiliated-activities",
          description: "Activities organized by affiliated associations",
        },
        {
          title: { en: "Social Welfare Activities", zh: "社會公益活動" },
          path: "#",
          description: "Community welfare and social responsibility programs",
          type: "grid",
          layout: "1x3",
          gridItems: [
            {
              title: { en: "Care Teams", zh: "關愛隊" },
              path: "/events/care-team",
              description: "Community care initiatives",
            },
            {
              title: { en: "Elderly Center", zh: "耆英中心" },
              path: "/about-kcc/elderly-center",
              description: "Elderly center services",
            },
            {
              title: { en: "Sea Scouts", zh: "海童軍" },
              path: "/events/sea-scouts",
              description: "Sea scouts activities",
            },
          ],
        },
        {
          title: { en: "Other Activities", zh: "其它活動" },
          path: "/events/other-activities",
          description: "Various other chamber activities",
        },
        {
          title: { en: "News Center", zh: "新聞中心" },
          path: "/news",
          description: "Latest news and announcements",
        },
      ],
    },
    {
      title: { en: "Contact Us", zh: "聯絡我們" },
      path: "/contact",
      icon: <Phone className="w-5 h-5" />,
      category: "contact",
      items: [
        {
          title: { en: "Join Our Association", zh: "加入本會" },
          path: "/about-kcc/join-us",
          description: "Information on how to become a member",
          subtitle: { en: "Friends of the Dragon Alumni Association", zh: "龍總之友" },
        },
        {
          title: { en: "Suggestion Box + Contact Us", zh: "意見箱+聯絡我們" },
          path: "#",
          description: "Share your suggestions or contact us directly",
          type: "split",
          splitItems: [
            {
              title: { en: "Suggestion Box", zh: "意見箱" },
              path: "/suggestion",
              description: "Submit your suggestions and feedback",
            },
            {
              title: { en: "Contact Us", zh: "聯絡我們" },
              path: "/contact",
              description: "Get in touch with us",
            },
          ],
        },
        {
          title: { en: "Rental Units", zh: "出租單位" },
          path: "/rental",
          description: "Information about available rental units",
        },
      ],
    },
  ]

  const categories = [
    { id: "all", label: "All Sections" },
    { id: "main", label: "Main" },
    { id: "about", label: "About" },
    { id: "events", label: "Events" },
    { id: "contact", label: "Contact" },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/50">
      <div className="container py-12 space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-lg bg-primary text-primary-foreground p-8 md:p-12">
          <div className="relative z-10">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">網站地圖 / Site Map</h1>
            <p className="text-primary-foreground/80 max-w-xl">
              Navigate through our comprehensive website structure. Find everything from latest news to detailed
              information about KCC.
            </p>
          </div>
          <div className="absolute inset-0 bg-[url('/placeholder.svg')] opacity-10" />
        </div>

        {/* Category Filter */}
        <div className="flex overflow-auto pb-2 -mx-2 px-2">
          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedSection === category.id ? "default" : "outline"}
                onClick={() => setSelectedSection(category.id)}
                className="whitespace-nowrap"
              >
                {category.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="grid gap-6">
          {siteStructure
            .filter((section) => selectedSection === "all" || section.category === selectedSection)
            .map((section, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 rounded-md bg-primary/10 text-primary">{section.icon}</div>
                    <h2 className="text-xl font-semibold">
                      <Link href={section.path} className="hover:text-primary transition-colors">
                        {section.title.zh} / {section.title.en}
                      </Link>
                    </h2>
                  </div>

                  {section.items && (
                    <div className="grid gap-4 pl-4">
                      {section.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="space-y-2">
                          {/* Main Item */}
                          <div className="group/item p-3 rounded-md hover:bg-muted transition-colors">
                            <div className="flex items-start gap-2">
                              <ChevronRight className="h-5 w-5 mt-0.5 text-muted-foreground group-hover/item:text-primary transition-colors" />
                              <div className="flex-1">
                                {item.path !== "#" ? (
                                  <Link href={item.path} className="font-medium group-hover/item:text-primary transition-colors">
                                    {item.title.zh} / {item.title.en}
                                  </Link>
                                ) : (
                                  <span className="font-medium text-foreground">
                                    {item.title.zh} / {item.title.en}
                                  </span>
                                )}
                                {item.subtitle && (
                                  <p className="text-xs text-muted-foreground mt-0.5">
                                    {item.subtitle.zh} / {item.subtitle.en}
                                  </p>
                                )}
                                {item.description && (
                                  <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Split Items */}
                          {item.type === "split" && item.splitItems && (
                            <div className="ml-8 grid gap-2 md:grid-cols-2">
                              {item.splitItems.map((splitItem, splitIndex) => (
                                <Link
                                  key={splitIndex}
                                  href={splitItem.path}
                                  className="group/split p-2 rounded-md hover:bg-muted/50 transition-colors border border-border/50"
                                >
                                  <div className="flex items-start gap-2">
                                    <ChevronRight className="h-4 w-4 mt-0.5 text-muted-foreground group-hover/split:text-primary transition-colors" />
                                    <div>
                                      <span className="text-sm font-medium group-hover/split:text-primary transition-colors">
                                        {splitItem.title.zh} / {splitItem.title.en}
                                      </span>
                                      {splitItem.description && (
                                        <p className="text-xs text-muted-foreground mt-0.5">{splitItem.description}</p>
                                      )}
                                    </div>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          )}

                          {/* Grid Items */}
                          {item.type === "grid" && item.gridItems && (
                            <div className={`ml-8 grid gap-2 ${
                              item.layout === "2x2" ? "grid-cols-1 md:grid-cols-2" :
                              item.layout === "1x3" ? "grid-cols-1 md:grid-cols-3" :
                              "grid-cols-1"
                            }`}>
                              {item.gridItems.map((gridItem, gridIndex) => (
                                <Link
                                  key={gridIndex}
                                  href={gridItem.path}
                                  className="group/grid p-2 rounded-md hover:bg-muted/50 transition-colors border border-border/50"
                                >
                                  <div className="flex items-start gap-2">
                                    <ChevronRight className="h-4 w-4 mt-0.5 text-muted-foreground group-hover/grid:text-primary transition-colors" />
                                    <div>
                                      <span className="text-sm font-medium group-hover/grid:text-primary transition-colors">
                                        {gridItem.title.zh} / {gridItem.title.en}
                                      </span>
                                      {gridItem.description && (
                                        <p className="text-xs text-muted-foreground mt-0.5">{gridItem.description}</p>
                                      )}
                                    </div>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
        </div>
      </div>
    </div>
  )
}

