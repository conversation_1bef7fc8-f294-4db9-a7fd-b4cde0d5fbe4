import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"
import Link from "next/link"
import AnimatedHero from "@/components/animated-hero"

const timelineEvents = [
  {
    year: "1938",
    title: "九龍潮商會創立",
    titleEn: "Establishment of Kowloon Chamber of Commerce",
    description: "九龍潮商會創自1938年，初名旨為商會，後改為旨在為旅港潮籍商人謀求福利及聯絡感情為宗旨。",
    descriptionEn:
      "The Kowloon Chamber of Commerce was established in 1938, initially to serve and connect Chaozhou merchants in Hong Kong.",
    image: "/placeholder.svg",
  },
  {
    year: "1945",
    title: "會務重組及擴展",
    titleEn: "Reorganization and Expansion",
    description:
      "1945年九龍各區商民團體參加，會員人數大增，乃改名為九龍潮商會，並用選舉制度，於該年12月28日舉行第一屆職員就職典禮。",
    descriptionEn:
      "In 1945, with the participation of various district business groups, the organization was renamed and held its first election.",
    image: "/placeholder.svg",
  },
  {
    year: "1947",
    title: "遷址彌敦道",
    titleEn: "Relocation to Nathan Road",
    description: "1947年添置會所於彌敦道，此後隨著社會的轉變，遷住大埔道。",
    descriptionEn: "In 1947, the Chamber acquired premises on Nathan Road, later relocating to Tai Po Road.",
    image: "/placeholder.svg",
  },
  {
    year: "1950",
    title: "建立永久會址",
    titleEn: "Establishment of Permanent Headquarters",
    description: "購置自由道96號為永久會址。會所經多次擴建及整修，現已集會所和商務於一身。",
    descriptionEn:
      "Purchased 96 Liberty Avenue as permanent headquarters, which has undergone several expansions and renovations.",
    image: "/placeholder.svg",
  },
  {
    year: "1951",
    title: "創辦英文中學",
    titleEn: "Establishment of English Secondary School",
    description: "1951年興建英文中學，為社會培育人才。",
    descriptionEn: "Established an English secondary school in 1951 to nurture talent for society.",
    image: "/placeholder.svg",
  },
  {
    year: "1956",
    title: "創辦中文小學",
    titleEn: "Establishment of Chinese Primary School",
    description: "1956年興建中文小學至功臣學校。",
    descriptionEn: "Established a Chinese primary school in 1956.",
    image: "/placeholder.svg",
  },
]

export default function HistoryPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "歷史",
          en: "History",
        }}
        description={{
          zh: "見證九龍總商會的發展歷程，傳承商界精神",
          en: "Witnessing the development of KCC, inheriting the spirit of business",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Introduction Section */}
        <section className="mb-16">
          <Card>
            <CardContent className="p-8">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-4">
                  <h2 className="text-2xl font-bold">商會簡介</h2>
                  <div className="space-y-4 text-muted-foreground">
                    <p>九龍潮商會創自1938年，初名旨為商會，後改為旨在為旅港潮籍商人謀求福利及聯絡感情為宗旨。</p>
                    <p>
                      1945年九龍各區商民團體參加，會員人數大增，乃改名為九龍潮商會，並用選舉制度，於該年12月28日舉行第一屆職員就職典禮，此後每屆選舉，三年任滿更換。
                    </p>
                  </div>
                  <Button asChild>
                    <Link href="/about">
                      了解更多 <ChevronRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </div>
                <div className="relative aspect-video md:aspect-square">
                  <Image src="/placeholder.svg" alt="Historical Photo" fill className="object-cover rounded-lg" />
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Timeline Section */}
        <section>
          <h2 className="text-2xl font-bold mb-8">發展里程 / Milestones</h2>
          <div className="space-y-12">
            {timelineEvents.map((event, index) => (
              <div key={event.year} className="relative">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  {index % 2 === 0 ? (
                    <>
                      <div className="relative aspect-video">
                        <Image
                          src={event.image || "/placeholder.svg"}
                          alt={event.title}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                      <div className="space-y-4">
                        <div className="inline-block bg-primary/10 text-primary px-4 py-1 rounded-full">
                          {event.year}
                        </div>
                        <h3 className="text-xl font-bold">
                          {event.title}
                          <span className="block text-base font-normal text-muted-foreground mt-1">
                            {event.titleEn}
                          </span>
                        </h3>
                        <p className="text-muted-foreground">{event.description}</p>
                        <p className="text-muted-foreground">{event.descriptionEn}</p>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="space-y-4">
                        <div className="inline-block bg-primary/10 text-primary px-4 py-1 rounded-full">
                          {event.year}
                        </div>
                        <h3 className="text-xl font-bold">
                          {event.title}
                          <span className="block text-base font-normal text-muted-foreground mt-1">
                            {event.titleEn}
                          </span>
                        </h3>
                        <p className="text-muted-foreground">{event.description}</p>
                        <p className="text-muted-foreground">{event.descriptionEn}</p>
                      </div>
                      <div className="relative aspect-video">
                        <Image
                          src={event.image || "/placeholder.svg"}
                          alt={event.title}
                          fill
                          className="object-cover rounded-lg"
                        />
                      </div>
                    </>
                  )}
                </div>
                {index < timelineEvents.length - 1 && (
                  <div className="absolute left-1/2 top-full h-12 w-px bg-primary/20" />
                )}
              </div>
            ))}
          </div>
        </section>

        {/* Historical Gallery */}
        <section className="mt-16">
          <h2 className="text-2xl font-bold mb-8">歷史圖片廊 / Historical Gallery</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="relative aspect-square group cursor-pointer">
                <Image
                  src="/placeholder.svg"
                  alt={`Historical Photo ${i + 1}`}
                  fill
                  className="object-cover rounded-lg"
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-lg">
                  <Button variant="outline" className="text-white border-white hover:text-white">
                    查看大圖
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}

