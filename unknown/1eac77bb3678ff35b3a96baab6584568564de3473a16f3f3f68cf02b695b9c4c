import { Metadata } from "next"
import PageHeader from "@/components/page-header"

export const metadata: Metadata = {
  title: "加入本會 (龍總之友) | Join Our Association - 九龍總商會",
  description: "Join the Kowloon Chamber of Commerce - Friends of the Dragon Alumni Association",
}

export default function JoinUsPage() {
  return (
    <main className="container py-8">
      <PageHeader
        title={{ zh: "加入本會", en: "Join Our Association" }}
        subtitle={{ zh: "龍總之友", en: "Friends of the Dragon Alumni Association" }}
        breadcrumbs={[
          { title: { zh: "主頁", en: "Home" }, href: "/" },
          { title: { zh: "聯絡我們", en: "Contact Us" }, href: "/contact" },
          { title: { zh: "加入本會", en: "Join Our Association" }, href: "/contact/join-us" },
        ]}
      />

      <div className="prose max-w-none mt-8">
        <h2>加入本會 - 龍總之友 / Join Our Association - Friends of the Dragon Alumni Association</h2>
        <p>
          感謝您對九龍總商會的興趣。如欲加入本會成為龍總之友，請填寫以下申請表格或聯繫我們的會員服務部門。
        </p>
        <p>
          Thank you for your interest in the Kowloon Chamber of Commerce. To join us as a member of the Friends of the Dragon Alumni Association, please fill out the application form below or contact our membership services department.
        </p>

        {/* Placeholder for membership application form */}
        <div className="bg-gray-100 p-6 rounded-lg mt-6">
          <h3>龍總之友申請表 / Friends of the Dragon Alumni Association Application Form</h3>
          <p>申請表格將在此顯示 / Application form will be displayed here</p>
        </div>
      </div>
    </main>
  )
}
