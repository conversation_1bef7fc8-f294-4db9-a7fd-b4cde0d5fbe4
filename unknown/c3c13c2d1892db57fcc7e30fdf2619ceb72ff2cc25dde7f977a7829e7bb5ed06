'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ImageIcon, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchBloodDonations, type BloodDonation } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import ImageSlider from '@/components/image-slider'

export default function CareTeamPage() {
  const [activities, setActivities] = useState<BloodDonation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 100;
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching care team activities (blood donations)...')
        const activitiesData = await fetchBloodDonations({ populate: '*' })
        console.log('Fetched care team activities:', activitiesData)

        if (activitiesData && activitiesData.length > 0) {
          setActivities(activitiesData) // Get all items
        } else {
          setError('No care team activities data found')
        }
      } catch (err) {
        console.error('Error fetching care team activities:', err)
        setError(err instanceof Error ? err.message : 'An error occurred while fetching data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (error) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title={{
            zh: "關愛隊",
            en: "Care Team",
          }}
          description={{
            zh: "了解九龍總商會關愛隊的社會公益活動和社區服務項目",
            en: "Learn about KCC Care Team's social welfare activities and community service projects",
          }}
          image="/placeholder.svg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "關愛隊",
          en: "Care Team",
        }}
        description={{
          zh: "了解九龍總商會關愛隊的社會公益活動和社區服務項目",
          en: "Learn about KCC Care Team's social welfare activities and community service projects",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">關愛隊 / Care Team</h2>
              <p>
                九龍總商會關愛隊致力於社會公益活動，包括血液捐贈、社區服務和慈善活動，為社會貢獻愛心和力量。
                我們定期組織各類關愛活動，為有需要的社群提供支援和幫助，體現商會的社會責任感。
              </p>
              <p>
                The KCC Care Team is dedicated to social welfare activities, including blood donations, community services, and charitable events, contributing love and strength to society.
                We regularly organize various caring activities to provide support and assistance to communities in need, reflecting the Chamber's sense of social responsibility.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Care Team Activities Listing */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Care Team Activities Items */}
              {activities.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">關愛隊活動</span> / Care Team Activities
                    </h2>
                    <p className="text-muted-foreground">關愛隊的社會公益活動 / Care Team's social welfare activities</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {activities.map((activity) => {
                      const isExpanded = expandedCards[activity.id]
                      const content = activity.impact_summary || activity.description
                      const showReadMore = shouldShowReadMore(content)

                      return (
                        <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {/* Image Slider for multiple images */}
                          {activity.image && activity.image.length > 0 ? (
                            <ImageSlider
                              images={activity.image.map(img => ({
                                id: img.id,
                                url: img.url,
                                formats: img.formats
                              }))}
                              alt={activity.event_name || "Care Team Activity Image"}
                              interval={3000}
                            />
                          ) : (
                            <div className="relative aspect-video">
                              <Image
                                src="/placeholder.svg"
                                alt={activity.event_name || "Care Team Activity"}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{activity.event_name}</h3>
                            <div className="space-y-2 mb-4">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{activity.location}</span>
                              </div>
                              {activity.organizer && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{activity.organizer}</span>
                                </div>
                              )}
                            </div>
                            <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                              {content}
                            </div>

                            {showReadMore && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(activity.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    Show Less <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    Read More <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    <span className="block">暫無關愛隊活動資料</span>
                    <span className="block">No care team activities available at the moment</span>
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}
