import { NextResponse } from "next/server"

export async function GET() {
  try {
    const strapiUrl = process.env.STRAPI_URL
    const strapiToken = process.env.STRAPI_TOKEN

    if (!strapiUrl || !strapiToken) {
      throw new Error("Missing Strapi configuration")
    }

    const response = await fetch(`${strapiUrl}/api/articles`, {
      headers: {
        Authorization: `Bearer ${strapiToken}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Strapi API error: ${response.statusText}`)
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error fetching from Strapi:", error)
    return NextResponse.json({ error: "Failed to fetch data from Strapi" }, { status: 500 })
  }
}

