"use client"

import type React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { motion } from "framer-motion"
import Link from "next/link"
import { ChevronRight } from "lucide-react"

interface SectionCardProps {
  title: {
    en: string
    zh: string
  }
  path: string
  icon: React.ReactNode
  items?: {
    title: {
      en: string
      zh: string
    }
    path: string
    description?: string
  }[]
}

export default function SectionCard({ title, path, icon, items }: SectionCardProps) {
  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
      <Card className="group hover:shadow-lg transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 rounded-md bg-primary/10 text-primary">{icon}</div>
            <h2 className="text-xl font-semibold">
              <Link href={path} className="hover:text-primary transition-colors">
                {title.en} / {title.zh}
              </Link>
            </h2>
          </div>

          {items && (
            <div className="grid gap-4 pl-4">
              {items.map((item, index) => (
                <Link
                  key={index}
                  href={item.path}
                  className="group/item p-3 rounded-md hover:bg-muted transition-colors"
                >
                  <div className="flex items-start gap-2">
                    <ChevronRight className="h-5 w-5 mt-0.5 text-muted-foreground group-hover/item:text-primary transition-colors" />
                    <div>
                      <span className="font-medium group-hover/item:text-primary transition-colors">
                        {item.title.en} / {item.title.zh}
                      </span>
                      {item.description && <p className="text-sm text-muted-foreground mt-1">{item.description}</p>}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

