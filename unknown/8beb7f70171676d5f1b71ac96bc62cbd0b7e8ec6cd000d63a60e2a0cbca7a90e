"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp, CalendarIcon } from "lucide-react"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchKeySpeeches } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"

interface Speech {
  id: number
  documentId: string
  title: string
  speaker?: string
  speakerEn?: string
  date?: string
  content?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>
  contentEn?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  category?: string
  description?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>
  order?: number
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: {
    url?: string
    formats?: {
      large?: {
        url: string
      }
      medium?: {
        url: string
      }
      small?: {
        url: string
      }
    }
  } | null
  seo?: any
  localizations?: any[]
}

interface SpeechCategory {
  id: string
  label: string
  labelEn: string
  speeches: Speech[]
}

export default function KeySpeechesPage() {
  const [activeTab, setActiveTab] = useState("")
  const [speechCategories, setSpeechCategories] = useState<SpeechCategory[]>([])
  const [expandedSpeeches, setExpandedSpeeches] = useState<number[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string>("");

  const toggleSpeech = (id: number) => {
    setExpandedSpeeches(prev =>
      prev.includes(id)
        ? prev.filter(speechId => speechId !== id)
        : [...prev, id]
    )
  }

  useEffect(() => {
    const loadSpeeches = async () => {
      try {
        setLoading(true)
        const response = await fetchKeySpeeches({
          populate: "*"
        })

        if (response && response.data && Array.isArray(response.data)) {
          // Log the first speech to debug the structure
          if (response.data.length > 0) {
            console.log("First speech data:", JSON.stringify(response.data[0], null, 2));
          }

          // Process the speeches data
          const speechesData = response.data as Speech[];
          const groupedSpeeches: Record<string, Speech[]> = {}

          // Group speeches by category
          speechesData.forEach(speech => {
            const category = speech.category || "Other"
            if (!groupedSpeeches[category]) {
              groupedSpeeches[category] = []
            }
            groupedSpeeches[category].push(speech)
          })

          // Sort speeches by date (newest first)
          Object.keys(groupedSpeeches).forEach(category => {
            groupedSpeeches[category].sort((a, b) => {
              if (!a.date && !b.date) return 0
              if (!a.date) return 1
              if (!b.date) return -1
              return new Date(b.date).getTime() - new Date(a.date).getTime()
            })
          })

          // Convert to array format for tabs
          const categories: SpeechCategory[] = [
            {
              id: "anniversary",
              label: "周年慶典演講",
              labelEn: "Anniversary Celebration Speeches",
              speeches: groupedSpeeches["Anniversary"] || []
            },
            {
              id: "agm",
              label: "年度會員大會演講",
              labelEn: "Annual General Meeting Speeches",
              speeches: groupedSpeeches["AGM"] || []
            },
            {
              id: "special",
              label: "特別場合演講",
              labelEn: "Special Occasion Speeches",
              speeches: groupedSpeeches["Special"] || []
            },
            {
              id: "other",
              label: "其他演講",
              labelEn: "Other Speeches",
              speeches: groupedSpeeches["Other"] || []
            }
          ].filter(category => category.speeches.length > 0)

          setSpeechCategories(categories)

          // Set active tab to first category if available
          if (categories.length > 0) {
            setActiveTab(categories[0].id)
          }

          // Count total speeches for debug info
          const totalSpeeches = categories.reduce((total, category) => total + category.speeches.length, 0);
          setDebugInfo(`Found ${totalSpeeches} speeches across ${categories.length} categories.`);
        }
        setLoading(false)
      } catch (err) {
        console.error("Error loading speeches:", err)
        setError("Failed to load speeches. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setSpeechCategories(fallbackCategories)
      }
    }

    loadSpeeches()
  }, [])

  // Update the fallback data to match the new content structure
  const fallbackCategories: SpeechCategory[] = [
    {
      id: "anniversary",
      label: "周年慶典演講",
      labelEn: "Anniversary Celebration Speeches",
      speeches: [
        {
          id: 1,
          documentId: "fallback-1",
          title: "九龍總商會75周年慶典演講",
          speaker: "張會長",
          speakerEn: "President Zhang",
          date: "2020-10-15",
          content: [
            {
              type: "paragraph",
              children: [
                {
                  text: "尊敬的各位嘉賓，各位會員，大家好！",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "今天我們齊聚一堂，慶祝九龍總商會成立75周年。這是一個非常重要的里程碑，標誌著我們商會在香港商界的長期貢獻和影響力。",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "謝謝大家！",
                  type: "text"
                }
              ]
            }
          ],
          contentEn: [
            {
              type: "paragraph",
              children: [
                {
                  text: "Distinguished guests, members, good afternoon!",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "Today we gather to celebrate the 75th anniversary of the Kowloon Chamber of Commerce. This is a significant milestone, marking our chamber's long-term contribution and influence in Hong Kong's business community.",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "Thank you all!",
                  type: "text"
                }
              ]
            }
          ],
          category: "Anniversary",
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        },
        {
          id: 2,
          documentId: "fallback-2",
          title: "九龍總商會70周年慶典演講",
          speaker: "李前會長",
          speakerEn: "Former President Li",
          date: "2015-10-10",
          content: [
            {
              type: "paragraph",
              children: [
                {
                  text: "各位嘉賓，各位會員，大家下午好！",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "七十年前，我們的前輩懷著服務社會的熱忱創立了九龍總商會。多年來，商會一直致力於促進香港商界的發展和繁榮。",
                  type: "text"
                }
              ]
            }
          ],
          contentEn: [
            {
              type: "paragraph",
              children: [
                {
                  text: "Distinguished guests and members, good afternoon!",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "Seventy years ago, our predecessors established the Kowloon Chamber of Commerce with enthusiasm for serving society. Over the years, the chamber has been committed to promoting the development and prosperity of Hong Kong's business community.",
                  type: "text"
                }
              ]
            }
          ],
          category: "Anniversary",
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        }
      ]
    },
    {
      id: "agm",
      label: "年度會員大會演講",
      labelEn: "Annual General Meeting Speeches",
      speeches: [
        {
          id: 3,
          documentId: "fallback-3",
          title: "2022年度會員大會會長演講",
          speaker: "張會長",
          speakerEn: "President Zhang",
          date: "2022-03-25",
          content: [
            {
              type: "paragraph",
              children: [
                {
                  text: "各位會員，大家好！",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "首先感謝大家在百忙之中抽空出席今天的年度會員大會。過去一年，在全體會員的共同努力下，我們克服了各種困難，取得了不少成績。",
                  type: "text"
                }
              ]
            }
          ],
          contentEn: [
            {
              type: "paragraph",
              children: [
                {
                  text: "Dear members, good day!",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "First of all, thank you all for taking time out of your busy schedules to attend today's Annual General Meeting. In the past year, with the joint efforts of all members, we have overcome various difficulties and achieved many accomplishments.",
                  type: "text"
                }
              ]
            }
          ],
          category: "AGM",
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        }
      ]
    },
    {
      id: "special",
      label: "特別場合演講",
      labelEn: "Special Occasion Speeches",
      speeches: [
        {
          id: 4,
          documentId: "fallback-4",
          title: "香港回歸25周年商會論壇演講",
          speaker: "張會長",
          speakerEn: "President Zhang",
          date: "2022-07-01",
          content: [
            {
              type: "paragraph",
              children: [
                {
                  text: "尊敬的各位嘉賓，各位朋友，大家好！",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "今天我們相聚在這個特別的日子，慶祝香港回歸祖國25周年。25年來，香港在「一國兩制」的框架下，保持了繁榮穩定，取得了舉世矚目的成就。",
                  type: "text"
                }
              ]
            }
          ],
          contentEn: [
            {
              type: "paragraph",
              children: [
                {
                  text: "Distinguished guests and friends, good day!",
                  type: "text"
                }
              ]
            },
            {
              type: "paragraph",
              children: [
                {
                  text: "Today we gather on this special day to celebrate the 25th anniversary of Hong Kong's return to the motherland. Over the past 25 years, Hong Kong has maintained prosperity and stability under the framework of 'One Country, Two Systems', achieving remarkable accomplishments that have attracted worldwide attention.",
                  type: "text"
                }
              ]
            }
          ],
          category: "Special",
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          publishedAt: "2023-01-01T00:00:00.000Z",
          locale: "zh"
        }
      ]
    }
  ]

  // Helper function to get image URL
  const getImageUrl = (speech: Speech) => {
    if (!speech.image) return "/placeholder.svg";
    return speech.image.formats?.medium?.url || speech.image.url || "/placeholder.svg";
  }

  // Helper function to get image alt text
  const getImageAlt = (speech: Speech) => {
    return speech.title || "Speech";
  }

  // Update the renderStructuredContent function to handle both string and structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      return content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          return (
            <p key={blockIndex} className="mb-4">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      });
    }

    return null;
  };

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "會長演講辭",
          en: "Key Speeches",
        }}
        description={{
          zh: "九龍總商會歷屆會長在重要場合的演講",
          en: "Speeches delivered by KCC presidents at important occasions",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">會長演講辭 / Presidential Speeches</h2>
              <p>
                九龍總商會歷屆會長在各種重要場合發表的演講，反映了商會的立場和觀點，
                也記錄了香港商界對社會經濟發展的思考。這些演講不僅是商會歷史的重要組成部分，
                也是研究香港商業發展的寶貴資料。
              </p>
              <p>
                Speeches delivered by presidents of the Kowloon Chamber of Commerce on various important occasions
                reflect the chamber's position and views, and also record the business community's thoughts on
                social and economic development. These speeches are not only an important part of the chamber's history,
                but also valuable materials for studying Hong Kong's business development.
              </p>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <div className="grid grid-cols-1 gap-6">
            {[1, 2].map((n) => (
              <Skeleton key={n} className="h-[600px] rounded-xl" />
            ))}
          </div>
        ) : error ? (
          <Card className="p-6">
            <p className="text-red-500">{error}</p>
            {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
          </Card>
        ) : speechCategories.length > 0 ? (
          <div className="space-y-8">
            {speechCategories.map((category) => (
              <div key={category.id} className="space-y-6">
                {category.id !== "other" && (
                  <h3 className="text-2xl font-bold">
                    {category.label} / {category.labelEn}
                  </h3>
                )}
                <div className="grid grid-cols-1 gap-8">
                  {category.speeches.length > 0 ? (
                    category.speeches.map((speech) => (
                      <Card key={speech.id} className="overflow-hidden">
                        <div className="grid md:grid-cols-[2fr,3fr] gap-6">
                          {/* Image Section */}
                          <div className="relative h-[400px] overflow-hidden bg-muted">
                            {speech.image ? (
                              <Image
                                src={getImageUrl(speech)}
                                alt={getImageAlt(speech)}
                                fill
                                className="object-cover transition-transform hover:scale-105"
                                sizes="(max-width: 768px) 100vw, 40vw"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full">
                                <p className="text-muted-foreground">No image available</p>
                              </div>
                            )}
                          </div>

                          {/* Content Section */}
                          <div className="p-6 space-y-6">
                            <div>
                              <h4 className="text-2xl font-bold mb-2">{speech.title}</h4>
                              {speech.speaker && (
                                <p className="text-lg text-muted-foreground">
                                  {speech.speaker}
                                  {speech.speakerEn && ` / ${speech.speakerEn}`}
                                </p>
                              )}
                              {speech.date && (
                                <div className="flex items-center gap-2 mt-2 text-muted-foreground">
                                  <CalendarIcon className="h-4 w-4" />
                                  <time dateTime={speech.date}>
                                    {new Date(speech.date).toLocaleDateString()}
                                  </time>
                                </div>
                              )}
                            </div>

                            <div className={cn(
                              "space-y-4 transition-all duration-300",
                              !expandedSpeeches.includes(speech.id) ? "line-clamp-3" : ""
                            )}>
                              <div className="prose prose-sm max-w-none">
                                {renderStructuredContent(speech.content)}
                                {speech.contentEn && (
                                  <div className="mt-4 pt-4 border-t">
                                    {renderStructuredContent(speech.contentEn)}
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="flex justify-center">
                              <Button
                                variant="outline"
                                onClick={() => toggleSpeech(speech.id)}
                                className="px-6"
                              >
                                {expandedSpeeches.includes(speech.id) ? (
                                  <>
                                    <ChevronUp className="h-4 w-4 mr-2" />
                                    顯示較少 / Show Less
                                  </>
                                ) : (
                                  <>
                                    <ChevronDown className="h-4 w-4 mr-2" />
                                    閱讀更多 / Read More
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))
                  ) : (
                    <Card className="p-6 text-center">
                      <p className="text-muted-foreground">No speeches available in this category</p>
                      {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
                    </Card>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Card className="p-6 text-center">
            <p className="text-muted-foreground">No speeches available</p>
            {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
          </Card>
        )}
      </div>
    </div>
  )
}