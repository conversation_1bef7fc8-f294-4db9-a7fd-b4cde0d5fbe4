"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"

interface VipInscriptionData {
  id: number;
  documentId: string;
  title: string;
  author?: string;
  date?: string;
  content?: {
    type: string;
    children: {
      text: string;
      type: string;
    }[];
  }[];
  order?: string;
  image?: {
    url: string;
    alternativeText?: string;
  };
}

export default function VipInscriptions() {
  const [inscriptionData, setInscriptionData] = useState<VipInscriptionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    async function fetchData() {
      try {
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae';

        setDebugInfo("Fetching data from API...");

        const response = await fetch(`${strapiUrl}/vip-inscriptions?populate=*`, {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log("API response:", data);

        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          const formattedInscriptions = data.data.map((item: any) => ({
            id: item.id,
            documentId: item.documentId,
            title: item.title,
            author: item.author,
            date: item.date,
            order: item.order,
            content: item.content,
            image: {
              url: item.image?.formats?.medium?.url || item.image?.url || "/placeholder.svg",
              alternativeText: item.image?.alternativeText || "VIP Inscription Image"
            }
          }));

          setInscriptionData(formattedInscriptions);
          setDebugInfo(`Found ${formattedInscriptions.length} VIP inscriptions.`);
        } else {
          setError("No VIP inscriptions found.");
        }

      } catch (err: any) {
        console.error("Error fetching VIP inscriptions data:", err);
        setError(`Failed to load data: ${err.message}`);
        setDebugInfo(`Error: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const renderContent = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p>No content available</p>;
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null
    });
  };

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{ zh: "名界題詞", en: "VIP Inscriptions" }}
        description={{ zh: "來自傑出人士的題詞", en: "Messages from distinguished individuals" }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">名界題詞 / VIP Inscriptions</h2>
              <p>
                九龍總商會有幸獲得多位政商界要人和社會知名人士的題詞。
                這些題詞凝聚了對商會的期望和鼓勵，也展示了商會在社會各界的影響力。
                我們珍藏這些寶貴的題詞，並將其精神融入商會的發展中。
              </p>
              <p>
                The Kowloon Chamber of Commerce has been fortunate to receive inscriptions from many political and business leaders and well-known social figures.
                These inscriptions embody expectations and encouragement for the Chamber, and also demonstrate the Chamber's influence in various sectors of society.
                We treasure these valuable inscriptions and incorporate their spirit into the development of the Chamber.
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardContent className="p-8">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            ) : inscriptionData.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {inscriptionData.map((inscription) => (
                  <Card key={inscription.id} className="overflow-hidden hover:shadow-lg transition-all">
                    <div className="relative aspect-square">
                      <Image
                        src={inscription.image?.url || "/placeholder.svg"}
                        alt={inscription.image?.alternativeText || `${inscription.title} - VIP Inscription`}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <CardContent className="p-4">
                      <h3 className="text-xl font-bold mb-1 truncate">{inscription.title}</h3>
                      <div className="space-y-1 mb-3">
                        {inscription.author && (
                          <p className="text-sm text-muted-foreground">
                            <strong>Author:</strong> {inscription.author}
                          </p>
                        )}
                        {inscription.date && (
                          <p className="text-sm text-muted-foreground">
                            <strong>Date:</strong> {inscription.date}
                          </p>
                        )}
                      </div>
                      {inscription.content && (
                        <div className="text-sm line-clamp-3 text-muted-foreground">
                          {inscription.content[0]?.children[0]?.text || ""}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No data available</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
