import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

export default function AboutKCC() {
  return (
    <div className="min-h-screen bg-[#002B5C]">
      {/* Hero Section */}
      <section className="relative h-[40vh] flex items-center justify-center">
        <div className="absolute inset-0">
          <Image src="/placeholder.svg" alt="About KCC" fill className="object-cover opacity-20" />
        </div>
        <div className="relative text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">關於本會 / About KCC</h1>
        </div>
      </section>

      <div className="container mx-auto py-12">
        <Card className="bg-[#003875] border-blue-900">
          <CardContent className="p-8">
            <div className="prose prose-invert max-w-none">
              <div className="grid md:grid-cols-2 gap-8 items-start">
                <div className="space-y-6">
                  <p className="text-blue-200">
                    九龍潮商會創自1938年，初名旨為商會，後改為旨在為旅港潮籍商人謀求福利及聯絡感情為宗旨。1945年九龍各區商民團體參加，會員人數大增，乃改名為九龍潮商會，並用選舉制度，於該年12月28日舉行第一屆職員就職典禮，此後每屆選舉，三年任滿更換。
                  </p>
                  <p className="text-blue-200">
                    1945年香港重光後，本會購於9月12日復會，及後會務蒸蒸日上，1947年添置會所於彌敦道，此後隨著社會的轉變，遷住大埔道，一度二度三度作為復興會所，公幹巨故遷出咸興里九龍巷，至三樓西洋樓，至1950年，購置自由道96號為永久會址。
                  </p>
                </div>
                <div className="relative aspect-video">
                  <Image src="/placeholder.svg" alt="KCC History" fill className="object-cover rounded-lg" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

