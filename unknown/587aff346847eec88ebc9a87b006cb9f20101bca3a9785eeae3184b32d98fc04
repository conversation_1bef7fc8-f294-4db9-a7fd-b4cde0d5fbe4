"use client"

import Link from "next/link"
import Image from "next/image"
import { Mail, Phone, MapPin } from "lucide-react"
import LanguageLink from "@/components/language-link"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

export default function FooterContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  const navigationLinks = [
    { href: "/", labelKey: "navigation.home" },
    { href: "/about-kcc/introduction", labelKey: "navigation.whatIsKcc" },
    { href: "/history/milestones", labelKey: "navigation.milestones" },
    { href: "/business-details", labelKey: "navigation.about" },
    { href: "/news", labelKey: "navigation.newsCenter" },
    { href: "/events/upcoming", labelKey: "navigation.chamberActivities" },
    { href: "/rental", labelKey: "navigation.rentalUnits" },
  ]

  return (
    <footer className="bg-[#1E1B4B] text-white">
      <div className="container mx-auto">
        {/* Logo and Contact Section */}
        <div className="flex flex-col md:flex-row justify-between items-start py-12 border-b border-blue-800">
          <div className="mb-8 md:mb-0">
            <div className="relative w-32 h-32 mb-6">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/WhatsApp%20Image%202025-02-25%20at%2014.29.07_509a9aaf.jpg-KDCEr9VlnMpFDB5hTK3jEgOuZmC0A7.jpeg"
                alt="KCC Logo"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <div className="space-y-4 text-blue-200">
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5 text-blue-400" />
              <p>{t('footer.email')}</p>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="h-5 w-5 text-blue-400" />
              <p>{t('footer.phone')}</p>
            </div>
            <div className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-blue-400" />
              <p>{t('footer.address')}</p>
            </div>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="py-8 border-b border-blue-800">
          <nav className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {navigationLinks.map((link) => (
              <LanguageLink key={link.labelKey} href={link.href} className="text-blue-200 hover:text-white transition-colors">
                <span className="block">{t(link.labelKey as any)}</span>
              </LanguageLink>
            ))}
          </nav>
        </div>

        {/* Copyright */}
        <div className="py-6 flex flex-col md:flex-row justify-between items-center text-sm text-blue-200">
          <p>{t('footer.copyright')}</p>
          <div className="flex items-center gap-4">
            <Link
              href="https://www.facebook.com/kcc"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-white transition-colors"
            >
              {t('footer.facebook')}
            </Link>
            <Link
              href="https://www.wechat.com/kcc"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-white transition-colors"
            >
              {t('footer.wechat')}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
