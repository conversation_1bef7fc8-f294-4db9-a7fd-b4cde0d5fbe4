import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, ChevronRight } from "lucide-react"
import Link from "next/link"
import AnimatedHero from "@/components/animated-hero"

const activityCategories = [
  {
    id: "reception",
    title: { zh: "接待禮賓", en: "Reception & Protocol" },
    description: "接待來自各地的商界代表團及政府機構，促進交流合作",
    link: "/events/reception"
  },
  {
    id: "exchange",
    title: { zh: "外訪交流", en: "External Exchange" },
    description: "組織會員企業赴各地考察訪問，開拓商機",
    link: "/events/exchange-visits"
  },
  {
    id: "community",
    title: { zh: "關心社區", en: "Community Care" },
    description: "參與社區服務，關注社會發展",
    link: "/events/community-care"
  },
  {
    id: "social",
    title: { zh: "社會服務", en: "Social Services" },
    description: "推動各項社會服務，回饋社會",
    link: "/about-kcc/elderly-center"
  },
  {
    id: "academic",
    title: { zh: "學術研討", en: "Academic Research" },
    description: "舉辦研討會及論壇，探討經濟發展趨勢",
    link: "/events/academic-research"
  },
]

const activities = [
  {
    id: 1,
    title: "接待深圳市商務局代表團",
    description: "本會接待深圳市商務局代表團到訪，就兩地經貿合作進行深入交流，共同探討大灣區發展機遇。",
    date: "2024-02-25",
    time: "14:30 - 16:30",
    location: "九龍總商會大樓",
    image: "/placeholder.svg",
    category: "reception",
    attendees: 30,
  },
  {
    id: 2,
    title: "大灣區考察團",
    description: "組織會員企業赴大灣區考察，實地了解當地營商環境和發展機遇，促進兩地商貿合作。",
    date: "2024-02-20",
    time: "09:00 - 18:00",
    location: "大灣區多個城市",
    image: "/placeholder.svg",
    category: "exchange",
    attendees: 45,
  },
  {
    id: 3,
    title: "社區長者探訪活動",
    description: "組織義工隊探訪區內長者，送上關懷與祝福，體現商會關愛社區的精神。",
    date: "2024-02-18",
    time: "10:00 - 12:00",
    location: "何文田邨",
    image: "/placeholder.svg",
    category: "community",
    attendees: 25,
  },
  {
    id: 4,
    title: "青年創業分享會",
    description: "邀請成功企業家分享創業經驗，為青年創業者提供指導和建議。",
    date: "2024-02-15",
    time: "15:00 - 17:00",
    location: "九龍總商會演講廳",
    image: "/placeholder.svg",
    category: "academic",
    attendees: 80,
  },
  {
    id: 5,
    title: "社區清潔日",
    description: "組織會員參與社區清潔活動，為建設美好社區出一分力。",
    date: "2024-02-12",
    time: "09:00 - 12:00",
    location: "九龍城區",
    image: "/placeholder.svg",
    category: "social",
    attendees: 50,
  },
]

export default function ActivitiesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "活動服務",
          en: "Activities",
        }}
        description={{
          zh: "九龍總商會致力促進商界交流，服務社會",
          en: "KCC is committed to promoting business exchanges and serving society",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Activity Categories */}
        <section className="mb-12">
          <div className="grid md:grid-cols-3 gap-6">
            {activityCategories.map((category) => (
              <Card key={category.id} className="hover:shadow-lg transition-all">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-2">
                    {category.title.zh}
                    <span className="block text-sm font-normal text-muted-foreground">{category.title.en}</span>
                  </h3>
                  <p className="text-muted-foreground mb-4">{category.description}</p>
                  <Button variant="outline" asChild>
                    <Link href={category.link}>
                      了解更多 <ChevronRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Activities Listing */}
        <section>
          <Tabs defaultValue="all" className="space-y-8">
            <TabsList className="w-full max-w-2xl mx-auto h-auto flex flex-wrap gap-2 bg-transparent p-0">
              <TabsTrigger
                value="all"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                全部活動
              </TabsTrigger>
              {activityCategories.map((category) => (
                <TabsTrigger
                  key={category.id}
                  value={category.id}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  {category.title.zh}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {activities.map((activity) => (
                  <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                    <div className="relative aspect-video">
                      <Image
                        src={activity.image || "/placeholder.svg"}
                        alt={activity.title}
                        fill
                        className="object-cover"
                      />
                      <Badge className="absolute top-4 left-4 bg-primary">
                        {activityCategories.find((cat) => cat.id === activity.category)?.title.zh}
                      </Badge>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold mb-3">{activity.title}</h3>
                      <p className="text-muted-foreground mb-4">{activity.description}</p>
                      <div className="space-y-2 text-sm text-muted-foreground mb-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{activity.date}</span>
                          <span>{activity.time}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <span>{activity.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          <span>{activity.attendees} 人參與</span>
                        </div>
                      </div>
                      <Button className="w-full">
                        活動詳情 <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {activityCategories.map((category) => (
              <TabsContent key={category.id} value={category.id}>
                <div className="grid md:grid-cols-2 gap-6">
                  {activities
                    .filter((activity) => activity.category === category.id)
                    .map((activity) => (
                      <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                        <div className="relative aspect-video">
                          <Image
                            src={activity.image || "/placeholder.svg"}
                            alt={activity.title}
                            fill
                            className="object-cover"
                          />
                          <Badge className="absolute top-4 left-4 bg-primary">{category.title.zh}</Badge>
                        </div>
                        <CardContent className="p-6">
                          <h3 className="text-xl font-bold mb-3">{activity.title}</h3>
                          <p className="text-muted-foreground mb-4">{activity.description}</p>
                          <div className="space-y-2 text-sm text-muted-foreground mb-4">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              <span>{activity.date}</span>
                              <span>{activity.time}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              <span>{activity.location}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              <span>{activity.attendees} 人參與</span>
                            </div>
                          </div>
                          <Button className="w-full">
                            活動詳情 <ChevronRight className="h-4 w-4 ml-2" />
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </section>
      </div>
    </div>
  )
}

