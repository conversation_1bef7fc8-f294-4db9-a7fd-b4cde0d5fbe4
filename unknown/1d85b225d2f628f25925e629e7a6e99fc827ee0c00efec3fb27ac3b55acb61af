"use client"

import { useState, useEffect } from "react"
import { useUser } from "@/contexts/user-context"
import { fetchUserRegistrations, Registration } from "@/lib/strapi"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import { format } from "date-fns"

export function UserEvents() {
  const { user } = useUser()
  const [registrations, setRegistrations] = useState<Registration[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set())

  useEffect(() => {
    const fetchRegistrations = async () => {
      if (!user) return

      try {
        const data = await fetchUserRegistrations(user.email)
        setRegistrations(data)
        setLoading(false)
      } catch (err) {
        console.error('Fetch error:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch registrations')
        setLoading(false)
      }
    }

    fetchRegistrations()
  }, [user])

  // Group registrations by event name
  const groupedRegistrations = registrations.reduce<Record<string, Registration[]>>((acc, registration) => {
    const eventName = registration.EventName
    if (!acc[eventName]) {
      acc[eventName] = []
    }
    acc[eventName].push(registration)
    return acc
  }, {})

  const toggleEvent = (eventName: string) => {
    setExpandedEvents(prev => {
      const newSet = new Set(prev)
      if (newSet.has(eventName)) {
        newSet.delete(eventName)
      } else {
        newSet.add(eventName)
      }
      return newSet
    })
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MM/dd/yyyy')
    } catch (e) {
      return dateString
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle>
            <span className="block mb-1">您的活動</span>
            <span className="text-base text-gray-600">Your Events</span>
          </CardTitle>
          <Button asChild className="bg-[#1E1B4B] hover:bg-[#1E1B4B]/90">
            <a href="/events/upcoming">
              即將舉行的活動 / Upcoming Events
            </a>
          </Button>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">載入中... / Loading...</div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle>
            <span className="block mb-1">您的活動</span>
            <span className="text-base text-gray-600">Your Events</span>
          </CardTitle>
          <Button asChild className="bg-[#1E1B4B] hover:bg-[#1E1B4B]/90">
            <a href="/events/upcoming">
              即將舉行的活動 / Upcoming Events
            </a>
          </Button>
        </CardHeader>
        <CardContent>
          <div className="text-red-500 p-4 bg-red-50 rounded-md mb-4">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <CardTitle>
          <span className="block mb-1">您的活動</span>
          <span className="text-base text-gray-600">Your Events</span>
        </CardTitle>
        <Button asChild className="bg-[#1E1B4B] hover:bg-[#1E1B4B]/90">
          <a href="/events/upcoming">
            即將舉行的活動 / Upcoming Events
          </a>
        </Button>
      </CardHeader>
      <CardContent>
        {Object.keys(groupedRegistrations).length > 0 ? (
          <div className="space-y-4">
            {Object.entries(groupedRegistrations).map(([eventName, eventRegistrations]) => (
              <div key={eventName} className="border rounded-md overflow-hidden">
                <div
                  className="flex justify-between items-center p-4 bg-[#1E1B4B]/5 cursor-pointer"
                  onClick={() => toggleEvent(eventName)}
                >
                  <div className="font-medium">{eventName}</div>
                  <Button variant="ghost" size="sm">
                    {expandedEvents.has(eventName) ? (
                      <ChevronUp className="h-5 w-5" />
                    ) : (
                      <ChevronDown className="h-5 w-5" />
                    )}
                  </Button>
                </div>

                {expandedEvents.has(eventName) && (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 border-collapse">
                      <thead className="bg-[#1E1B4B]/5">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">姓名 / Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">電郵 / Email</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">電話 / Phone</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">機構 / Organization</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">提交日期 / Submitted</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {eventRegistrations.map((registration) => (
                          <tr key={registration.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{registration.Name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{registration.email}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{registration.Number}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{registration.OrganizationName}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{formatDate(registration.createdAt)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">您尚未註冊任何活動 / You haven't registered for any events yet</p>
            <Button asChild>
              <a href="/events/upcoming">瀏覽活動 / Browse Events</a>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
