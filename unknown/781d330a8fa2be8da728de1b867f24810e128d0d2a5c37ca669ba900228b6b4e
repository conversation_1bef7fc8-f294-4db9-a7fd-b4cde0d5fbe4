'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, Building, Users, Award } from 'lucide-react'
import AnimatedHero from '@/components/animated-hero'

// Mock data for KCC development history - replace with actual Strapi data when available
const mockKCCDevelopment = [
  {
    id: 1,
    title: "九龍總商會成立",
    titleEn: "Establishment of KCC",
    description: "九龍總商會正式成立，旨在促進九龍地區商業發展，為會員提供商業支援和交流平台。",
    descriptionEn: "The Kowloon Chamber of Commerce was officially established to promote business development in Kowloon and provide business support and exchange platforms for members.",
    year: "1948",
    significance: "奠定了九龍商業發展的基礎",
    significanceEn: "Laid the foundation for Kowloon's business development",
    image: "/placeholder.svg"
  },
  {
    id: 2,
    title: "首屆會址設立",
    titleEn: "First Office Establishment",
    description: "在九龍設立首個正式會址，為會員提供聚會和商業活動場所。",
    descriptionEn: "Established the first official office in Kowloon, providing meeting and business activity venues for members.",
    year: "1950",
    significance: "為商會發展提供了穩定的基地",
    significanceEn: "Provided a stable base for chamber development",
    image: "/placeholder.svg"
  },
  {
    id: 3,
    title: "會員制度完善",
    titleEn: "Membership System Enhancement",
    description: "建立完善的會員制度，吸引更多優秀企業加入，擴大商會影響力。",
    descriptionEn: "Established a comprehensive membership system, attracting more excellent enterprises to join and expanding the chamber's influence.",
    year: "1960",
    significance: "奠定了現代商會運作模式",
    significanceEn: "Established the modern chamber operation model",
    image: "/placeholder.svg"
  },
  {
    id: 4,
    title: "國際交流拓展",
    titleEn: "International Exchange Expansion",
    description: "開始與海外商會建立合作關係，促進國際商業交流與合作。",
    descriptionEn: "Began establishing cooperative relationships with overseas chambers, promoting international business exchange and cooperation.",
    year: "1980",
    significance: "開啟了國際化發展道路",
    significanceEn: "Opened the path to international development",
    image: "/placeholder.svg"
  }
]

export default function KCCDevelopmentPage() {
  const [developments, setDevelopments] = useState(mockKCCDevelopment)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (error) {
    return (
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: "龍總發展史簡",
            en: "KCC Development History",
          }}
          description={{
            zh: "九龍總商會發展歷程的重要階段和關鍵時刻",
            en: "Important stages and key moments in KCC's development journey",
          }}
          image="/placeholder.svg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "龍總發展史簡",
          en: "KCC Development History",
        }}
        description={{
          zh: "九龍總商會發展歷程的重要階段和關鍵時刻",
          en: "Important stages and key moments in KCC's development journey",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">龍總發展史簡 / KCC Development History</h2>
              <p>
                九龍總商會自1948年成立以來，經歷了七十多年的風雨歷程，從一個地區性的商業組織發展成為具有重要影響力的商會。
                我們見證了香港經濟的起飛，參與了社會的發展，為促進商業繁榮和社會進步作出了重要貢獻。
              </p>
              <p>
                Since its establishment in 1948, the Kowloon Chamber of Commerce has experienced over 70 years of development,
                evolving from a regional business organization into an influential chamber of commerce.
                We have witnessed Hong Kong's economic takeoff, participated in social development, and made important contributions to promoting business prosperity and social progress.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Development Timeline */}
        <section>
          {loading ? (
            <div className="space-y-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-8 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {developments.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">發展歷程</span> / Development Timeline
                    </h2>
                    <p className="text-muted-foreground">九龍總商會重要發展階段 / Important development stages of KCC</p>
                  </div>

                  <div className="relative">
                    {/* Timeline line */}
                    <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary to-primary/20"></div>
                    
                    <div className="space-y-8">
                      {developments.map((development, index) => (
                        <div key={development.id} className="relative flex items-start gap-6">
                          {/* Timeline dot */}
                          <div className="relative z-10 flex-shrink-0">
                            <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                              {development.year}
                            </div>
                          </div>
                          
                          {/* Content card */}
                          <Card className="flex-1 hover:shadow-lg transition-all">
                            <CardContent className="p-6">
                              <div className="flex items-start gap-4">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                                    <Calendar className="h-4 w-4" />
                                    <span>{development.year}年</span>
                                  </div>
                                  
                                  <h3 className="text-xl font-bold mb-2">{development.title}</h3>
                                  <p className="text-muted-foreground mb-4">{development.description}</p>
                                  
                                  <div className="flex items-start gap-2 bg-blue-50 p-3 rounded-lg border border-blue-200">
                                    <Award className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                                    <div>
                                      <div className="font-medium text-blue-900">重要意義 / Significance</div>
                                      <div className="text-sm text-blue-700">{development.significance}</div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="w-32 h-24 relative flex-shrink-0">
                                  <Image
                                    src={development.image}
                                    alt={development.title}
                                    fill
                                    className="object-cover rounded"
                                  />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    <span className="block">暫無發展歷程資料</span>
                    <span className="block">No development history available at the moment</span>
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}
