import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Building2, ArrowRight, ExternalLink } from "lucide-react"
import Link from "next/link"

export default function DynamicBusiness() {
  return (
    <div className="min-h-screen bg-zinc-50">
      {/* Header */}
      <header className="bg-white border-b sticky top-0 z-50">
        <div className="container mx-auto">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center gap-2">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">KCC</span>
              </div>
              <span className="font-semibold">Kowloon Chamber</span>
            </Link>
            <nav className="flex items-center gap-8">
              <Link href="/about-kcc" className="hover:text-primary">
                About
              </Link>
              <Link href="/events" className="hover:text-primary">
                Events
              </Link>
              <Link href="/business" className="hover:text-primary">
                Business
              </Link>
              <Button variant="outline" className="ml-4">
                Join KCC
              </Button>
              <div className="flex gap-2 text-sm">
                <Link href="?lang=zh">繁體</Link>
                <Link href="?lang=cn">简体</Link>
                <Link href="?lang=en">Eng</Link>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-primary/10 to-transparent">
        <div className="container mx-auto py-20">
          <div className="max-w-2xl">
            <Badge className="mb-4">Hong Kong KCC Elite Association</Badge>
            <h1 className="text-5xl font-bold mb-6 leading-tight">Empowering Business Growth in Hong Kong</h1>
            <p className="text-xl text-muted-foreground mb-8">
              Join our network of successful businesses and entrepreneurs
            </p>
            <div className="flex gap-4">
              <Button size="lg">Become a Member</Button>
              <Button variant="outline" size="lg">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* News & Events */}
      <section className="container mx-auto py-16">
        <div className="grid md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Latest News
                <Link href="/news" className="text-sm font-normal text-muted-foreground hover:text-primary">
                  View All
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <article className="group">
                  <time className="text-sm text-muted-foreground">2021-01-01</time>
                  <h3 className="text-lg font-semibold group-hover:text-primary">2021年龍總活動</h3>
                  <p className="text-muted-foreground mt-2">Event details and description...</p>
                  <Link href="/news/1" className="inline-flex items-center gap-2 text-primary mt-2 hover:underline">
                    Read More <ArrowRight className="w-4 h-4" />
                  </Link>
                </article>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Upcoming Events
                <Link href="/events" className="text-sm font-normal text-muted-foreground hover:text-primary">
                  View All
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent>{/* Event content */}</CardContent>
          </Card>
        </div>
      </section>

      {/* Business Opportunities */}
      <section className="bg-white py-16">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold mb-12 text-center">Business Opportunities</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Link href="/business/local" className="group">
              <Card className="hover:shadow-lg transition-all">
                <CardContent className="p-6">
                  <Building2 className="w-12 h-12 text-primary mb-4" />
                  <h3 className="font-semibold text-lg mb-2 group-hover:text-primary">Local Business</h3>
                  <p className="text-muted-foreground">Connect with local enterprises and expand your network</p>
                </CardContent>
              </Card>
            </Link>
            {/* Add more business cards */}
          </div>
        </div>
      </section>

      {/* External Links */}
      <section className="container mx-auto py-16">
        <h2 className="text-2xl font-bold mb-8">Partner Organizations</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link href="http://www.locpg.hk/" target="_blank" className="group">
            <Card>
              <CardContent className="p-4 flex items-center gap-2">
                <span className="group-hover:text-primary">Liaison Office</span>
                <ExternalLink className="w-4 h-4" />
              </CardContent>
            </Card>
          </Link>
          {/* Add more partner links */}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-zinc-900 text-white">
        <div className="container mx-auto py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="font-semibold mb-4">Contact</h3>
              <div className="space-y-2 text-zinc-400">
                <p><EMAIL></p>
                <p>Tel: 2760 0393</p>
                <p>3/F KCC Building, 2 Liberty Avenue, Kowloon</p>
              </div>
            </div>
            {/* Add more footer columns */}
          </div>
          <div className="border-t border-zinc-800 mt-8 pt-8 text-center text-zinc-400">
            <p>Copyright © 2024 Kowloon Chamber of Commerce</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

