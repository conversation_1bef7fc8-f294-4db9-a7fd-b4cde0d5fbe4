import { Suspense } from "react"
import HomeContent from "./home-content"

function LoadingFallback() {
  return (
    <div className="min-h-screen">
      {/* Hero Section Skeleton */}
      <div className="relative h-[60vh] bg-gray-200 animate-pulse">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-300 to-gray-200" />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="h-12 bg-gray-300 rounded w-96 mx-auto mb-4 animate-pulse" />
            <div className="h-6 bg-gray-300 rounded w-64 mx-auto animate-pulse" />
          </div>
        </div>
      </div>

      <div className="container py-16">
        <div className="grid md:grid-cols-3 gap-8">
          {Array(3).fill(null).map((_, index) => (
            <div key={`skeleton-${index}`} className="bg-white rounded-lg shadow-sm border p-6">
              <div className="h-6 bg-gray-200 rounded w-6 mb-4 animate-pulse" />
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-2 animate-pulse" />
              <div className="h-16 bg-gray-200 rounded w-full animate-pulse" />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function Home() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <HomeContent />
    </Suspense>
  )
}

