import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import Breadcrumb from "@/components/breadcrumb"
import AnimatedHero from "@/components/animated-hero"

// Sample historical data - in production, this would come from your CMS
const historicalBoards = [
  {
    term: "第二十屆 (2020-2023)",
    members: [
      {
        role: "會長",
        name: "陳德濂",
        image: "/placeholder.svg",
        company: "致富集團控股有限公司",
        position: "主席",
      },
      {
        role: "副會長",
        name: "林宣武",
        image: "/placeholder.svg",
        company: "信興集團",
        position: "主席",
      },
      // Add more members...
    ],
  },
  {
    term: "第十九屆 (2017-2020)",
    members: [
      {
        role: "會長",
        name: "林宣武",
        image: "/placeholder.svg",
        company: "信興集團",
        position: "主席",
      },
      // Add more members...
    ],
  },
  {
    term: "第十八屆 (2014-2017)",
    members: [
      {
        role: "會長",
        name: "李秀恒",
        image: "/placeholder.svg",
        company: "富豪織造廠有限公司",
        position: "董事長",
      },
      // Add more members...
    ],
  },
]

const breadcrumbItems = [
  { title: { en: "Home", zh: "主頁" }, path: "/" },
  { title: { en: "About KCC", zh: "關於本會" }, path: "/about" },
  { title: { en: "Board Members", zh: "理監事會" }, path: "/about/board-members" },
  { title: { en: "Historical Board Members", zh: "歷屆理監事名表" }, path: "/about/board-members/historical" },
]

export default function HistoricalBoardMembersPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "歷屆理監事名表",
          en: "Historical Board Members",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbItems} />

        {/* Main Content */}
        <div className="mt-8">
          <Tabs defaultValue={historicalBoards[0].term} className="space-y-8">
            <TabsList className="w-full max-w-2xl mx-auto h-auto flex flex-wrap gap-2 bg-transparent p-0">
              {historicalBoards.map((board) => (
                <TabsTrigger
                  key={board.term}
                  value={board.term}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  {board.term}
                </TabsTrigger>
              ))}
            </TabsList>

            {historicalBoards.map((board) => (
              <TabsContent key={board.term} value={board.term}>
                <div className="grid gap-8">
                  <Card>
                    <CardContent className="p-6">
                      <h2 className="text-2xl font-bold mb-6">{board.term}</h2>
                      <div className="grid gap-8">
                        {board.members.map((member, index) => (
                          <div key={index} className="flex gap-6 items-start">
                            <div className="relative w-32 h-40 flex-shrink-0">
                              <Image
                                src={member.image || "/placeholder.svg"}
                                alt={member.name}
                                fill
                                className="object-cover rounded-lg"
                              />
                            </div>
                            <div>
                              <div className="inline-block bg-primary/10 text-primary px-3 py-1 rounded-full text-sm mb-2">
                                {member.role}
                              </div>
                              <h3 className="text-xl font-bold mb-2">{member.name}</h3>
                              <p className="text-muted-foreground">{member.company}</p>
                              <p className="text-muted-foreground">{member.position}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>

        {/* Historical Timeline */}
        <section className="mt-16">
          <h2 className="text-2xl font-bold mb-8">歷史里程碑 / Historical Milestones</h2>
          <div className="relative border-l border-primary/20 pl-8 space-y-8">
            {historicalBoards.map((board, index) => (
              <div key={index} className="relative">
                <div className="absolute -left-10 w-3 h-3 bg-primary rounded-full" />
                <div className="mb-2">
                  <span className="text-lg font-bold">{board.term}</span>
                </div>
                <Card>
                  <CardContent className="p-6">
                    <div className="space-y-2">
                      {board.members.map((member, memberIndex) => (
                        <p key={memberIndex}>
                          <span className="text-muted-foreground">{member.role}：</span>
                          <span className="font-medium">{member.name}</span>
                        </p>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}

