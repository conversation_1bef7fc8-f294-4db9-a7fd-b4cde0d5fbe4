"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { fetchListOfCouncilors } from "@/lib/strapi"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { getLanguageFromParams, useTranslation } from "@/lib/translations"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId?: string
  name?: string
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: StrapiImageFormats
  url?: string
}

interface CouncilorData {
  id: number
  documentId?: string
  year?: string
  position?: string
  name?: string
  biography?: any[]
  description?: any[]
  order?: number
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  locale?: string
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

interface StrapiResponse {
  data: Array<{
    id: number
    attributes?: CouncilorData
  } & CouncilorData>
  meta?: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

function CouncilorsContent() {
  const searchParams = useSearchParams()
  const [councilorData, setCouncilorData] = useState<CouncilorData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCouncilor, setSelectedCouncilor] = useState<CouncilorData | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  useEffect(() => {
    const loadCouncilors = async () => {
      try {
        setLoading(true)
        const locale = getStrapiLocale

        console.log("Councilors page - Current language:", currentLang)
        console.log("Councilors page - Strapi locale:", locale)

        const response = await fetchListOfCouncilors({
          populate: "*",
          sort: "order:asc",
          locale: locale
        }) as StrapiResponse

        console.log("Councilors page - API response:", response)

        // Process the data similar to council member page
        const processedData = response.data.map(item => {
          const processedItem = { ...item };
          if (item.attributes) {
            Object.assign(processedItem, item.attributes);
            processedItem.id = item.id;

            // Handle image data from Strapi v4 format
            if (item.attributes.image && (item.attributes.image as any).data) {
              const imageData = (item.attributes.image as any).data;
              if (Array.isArray(imageData)) {
                processedItem.images = imageData.map((img: any) => ({
                  id: img.id,
                  ...img.attributes || img
                }));
              } else {
                processedItem.image = {
                  id: imageData.id,
                  ...imageData.attributes || imageData
                };
              }
            }
          } else if (item.image && Array.isArray(item.image) && item.image.length > 0) {
            processedItem.images = item.image.map(img => ({
              id: img.id,
              url: img.url,
              formats: img.formats
            }));
          }
          return processedItem;
        });

        setCouncilorData(processedData)
        console.log("Councilors page - Processed data:", processedData)

      } catch (err: any) {
        console.error("Error fetching councilors:", err)

        // Provide more detailed error information
        let errorMessage = t('pastCouncils.error');
        if (err.message.includes('403')) {
          errorMessage = "API access forbidden. Please check if the list-of-councilors endpoint is properly configured in Strapi with correct permissions.";
        } else if (err.message.includes('404')) {
          errorMessage = "Councilors endpoint not found. Please verify the API endpoint exists.";
        } else if (err.message.includes('Failed to fetch')) {
          errorMessage = "Network error. Please check your internet connection and try again.";
        }

        setError(errorMessage)
      } finally {
        setLoading(false)
      }
    }

    loadCouncilors()
  }, [currentLang]) // Only re-run when the actual language changes

  // Function to render rich text content (simplified - no read more functionality)
  const renderContent = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p>No content available</p>;
    }

    return (
      <div className="space-y-4">
        {content.map((block, index) => {
          if (block.type === "paragraph") {
            return (
              <p key={index} className="mb-4 text-sm text-muted-foreground">
                {block.children.map((child: any, childIndex: number) => (
                  <span key={childIndex}>{child.text}</span>
                ))}
              </p>
            );
          }
          return null;
        })}
      </div>
    );
  };

  // Function to get a brief excerpt for the card view
  const getBriefExcerpt = (content: any[]) => {
    if (!content || !Array.isArray(content) || content.length === 0) {
      return "";
    }

    const firstParagraph = content[0];
    if (firstParagraph?.children && Array.isArray(firstParagraph.children)) {
      const text = firstParagraph.children
        .map((child: any) => child.text || "")
        .join("");
      // Return first 150 characters with ellipsis
      return text.length > 150 ? text.substring(0, 150) + "..." : text;
    }

    return "";
  };

  // Function to get councilor content (description or biography)
  const getCouncilorContent = (councilor: CouncilorData) => {
    // Prioritize description field, fallback to biography
    return councilor.description || councilor.biography || [];
  };

  // Function to open the modal with the selected councilor
  const openCouncilorDetails = (councilor: CouncilorData) => {
    setSelectedCouncilor(councilor);
    setIsModalOpen(true);
  }

  // Function to get optimal image URL
  const getOptimalImageUrl = (image: StrapiImage) => {
    if (image.formats?.large?.url) return image.formats.large.url;
    if (image.formats?.medium?.url) return image.formats.medium.url;
    if (image.formats?.small?.url) return image.formats.small.url;
    return image.url || "/placeholder.svg";
  };

  // Get best image URL for a councilor
  const getImageUrl = (councilor: CouncilorData) => {
    if (!councilor?.image) return "/placeholder.svg"

    if (Array.isArray(councilor.image)) {
      return councilor.image.length > 0 ? getOptimalImageUrl(councilor.image[0]) : "/placeholder.svg"
    } else {
      return getOptimalImageUrl(councilor.image)
    }
  }

  const getStaticTitle = () => {
    return t('pastCouncils.staticTitle');
  };

  const getStaticDescription = () => {
    return t('pastCouncils.staticDescription');
  };

  if (error) {
    return (
      <div className="container py-12">
        <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
          <h2 className="text-xl font-semibold text-red-700 mb-2">
            {currentLang === 'en' ? (
              <span className="block">An error occurred</span>
            ) : currentLang === 'cn' ? (
              <span className="block">发生错误</span>
            ) : (
              <span className="block">發生錯誤</span>
            )}
          </h2>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-12 space-y-8">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{getStaticTitle()}</h2>
            <p>
              {getStaticDescription()}
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          // Loading skeletons
          Array(6).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-video bg-muted animate-pulse" />
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
                  <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
                  <div className="space-y-2">
                    <div className="h-2 bg-muted rounded animate-pulse" />
                    <div className="h-2 bg-muted rounded animate-pulse w-5/6" />
                    <div className="h-2 bg-muted rounded animate-pulse w-4/6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : councilorData.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-muted-foreground">{t('pastCouncils.noCouncilors')}</p>
          </div>
        ) : (
          councilorData.map((councilor) => {
            const councilorName = councilor.name || `Councilor ${councilor.id}`
            const councilorPosition = councilor.position || ""
            const councilorYear = councilor.year || ""

            // Handle images - check if councilor has images array or single image
            const councilorImages = councilor.images || (councilor.image && Array.isArray(councilor.image) ? councilor.image : councilor.image ? [councilor.image] : [])

            return (
              <Card key={councilor.id} className="overflow-hidden">
                <CardContent className="p-0">
                  {(() => {
                    const mediaList = patchMediaMime(councilorImages);
                    if (mediaList.length > 1) {
                      return (
                        <FullImageSlider
                          images={mediaList}
                          alt={councilorName}
                          interval={3000}
                        />
                      );
                    } else if (mediaList.length === 1) {
                      return (
                        <div className="relative aspect-[4/3] w-full min-h-[1px]">
                          {(typeof (mediaList[0] as any).mime === 'string' && (mediaList[0] as any).mime.startsWith('video/')) ? (
                            <video
                              src={String(mediaList[0].url) || '/placeholder.svg'}
                              poster={String((mediaList[0] as any).previewUrl) || ''}
                              controls
                              className="w-full h-full object-contain"
                              playsInline
                            />
                          ) : (
                            <Image
                              src={mediaList[0].url ? String(mediaList[0].url) : '/placeholder.svg'}
                              alt={councilorName}
                              fill
                              className="object-contain"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          )}
                        </div>
                      );
                    } else {
                      return (
                        <div className="aspect-video bg-muted flex items-center justify-center">
                          <span className="text-4xl">👤</span>
                        </div>
                      );
                    }
                  })()}
                  <div className="p-6 space-y-4">
                    <h3 className="text-xl font-semibold">{councilorName}</h3>
                    {councilorPosition && (
                      <p className="text-muted-foreground font-medium">{councilorPosition}</p>
                    )}
                    {councilorYear && (
                      <p className="text-sm text-muted-foreground">{councilorYear}</p>
                    )}

                    {/* Brief Description Preview */}
                    {getCouncilorContent(councilor).length > 0 && (
                      <div className="text-sm text-muted-foreground line-clamp-3">
                        {getBriefExcerpt(getCouncilorContent(councilor))}
                      </div>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-2"
                      onClick={() => openCouncilorDetails(councilor)}
                    >
                      {t('pastCouncils.viewDetails')}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}
      </div>

      {/* Councilor Details Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        {selectedCouncilor && (
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-2xl">{selectedCouncilor.name}</DialogTitle>
              {selectedCouncilor.position && (
                <DialogDescription className="text-base">
                  <span className="font-medium">{t('pastCouncils.position')}:</span> {selectedCouncilor.position}
                  {selectedCouncilor.year && (
                    <span className="ml-4">
                      <span className="font-medium">{t('pastCouncils.year')}:</span> {selectedCouncilor.year}
                    </span>
                  )}
                </DialogDescription>
              )}
            </DialogHeader>

            <div className="grid md:grid-cols-2 gap-6 mt-4">
              {selectedCouncilor && (() => {
                const modalImages = selectedCouncilor.images || (Array.isArray(selectedCouncilor.image) ? selectedCouncilor.image : selectedCouncilor.image ? [selectedCouncilor.image] : [])
                const modalMedia = patchMediaMime(modalImages);
                if (modalMedia.length > 1) {
                  return (
                    <FullImageSlider
                      images={modalMedia}
                      alt={selectedCouncilor.name || "Councilor"}
                      interval={4000}
                    />
                  );
                } else if (modalMedia.length === 1) {
                  return (
                    <div className="relative aspect-square w-full min-h-[1px]">
                      {(typeof (modalMedia[0] as any).mime === 'string' && (modalMedia[0] as any).mime.startsWith('video/')) ? (
                        <video
                          src={String(modalMedia[0].url) || '/placeholder.svg'}
                          poster={String((modalMedia[0] as any).previewUrl) || ''}
                          controls
                          className="w-full h-full object-contain rounded-lg"
                          playsInline
                        />
                      ) : (
                        <Image
                          src={modalMedia[0].url ? String(modalMedia[0].url) : '/placeholder.svg'}
                          alt={selectedCouncilor.name || "Councilor"}
                          fill
                          className="object-contain rounded-lg"
                          sizes="(max-width: 768px) 100vw, 50vw"
                        />
                      )}
                    </div>
                  );
                } else {
                  return (
                    <div className="relative aspect-square">
                      <Image
                        src={getImageUrl(selectedCouncilor) || '/placeholder.svg'}
                        alt={selectedCouncilor.name || 'Councilor'}
                        fill
                        className="object-cover rounded-lg"
                        onError={(e) => {
                          console.error(`Error loading image for ${selectedCouncilor.name}`);
                          (e.target as HTMLImageElement).src = "/placeholder.svg";
                        }}
                      />
                    </div>
                  );
                }
              })()}

              <div className="prose prose-sm max-w-none">
                {renderContent(getCouncilorContent(selectedCouncilor))}
              </div>
            </div>
          </DialogContent>
        )}
      </Dialog>
    </div>
  )
}

function CouncilorsLoading() {
  return (
    <div className="min-h-screen">
      <div className="relative h-96 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="h-8 bg-white/20 rounded animate-pulse w-64 mx-auto mb-4" />
          <div className="h-4 bg-white/20 rounded animate-pulse w-96 mx-auto" />
        </div>
      </div>

      <div className="container py-12 space-y-8">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="h-6 bg-muted rounded animate-pulse w-48 mx-auto" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                <div className="h-4 bg-muted rounded animate-pulse w-4/6 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array(6).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-video bg-muted animate-pulse" />
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
                  <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
                  <div className="space-y-2">
                    <div className="h-2 bg-muted rounded animate-pulse" />
                    <div className="h-2 bg-muted rounded animate-pulse w-5/6" />
                    <div className="h-2 bg-muted rounded animate-pulse w-4/6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Component that uses searchParams
function CouncilorsPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="pastCouncils.title"
        description="pastCouncils.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<CouncilorsLoading />}>
        <CouncilorsContent />
      </Suspense>
    </div>
  )
}

// Main export with Suspense wrapper
export default function Councilors() {
  return (
    <Suspense fallback={<CouncilorsLoading />}>
      <CouncilorsPage />
    </Suspense>
  )
}