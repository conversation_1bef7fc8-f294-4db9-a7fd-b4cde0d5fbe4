"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchChineseMedicineDevelopments } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
// Badge import removed as it's no longer used
// import { Badge } from "@/components/ui/badge"
import { Calendar, Tag, ExternalLink } from "lucide-react"
// FileText import removed as it's not used
import ImageSlider from "@/components/image-slider"

interface ChineseMedicine {
  id: number
  documentId: string
  title: string
  content?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  category?: string
  publicationDate?: string
  externalLink?: string
  image?: {
    id: number
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  } | Array<{
    id: number
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }>
  images?: Array<{
    id: number
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }>
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
}

export default function ChineseMedicinePage() {
  const [medicineInfo, setMedicineInfo] = useState<ChineseMedicine[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    const loadMedicineInfo = async () => {
      try {
        setLoading(true)
        const response = await fetchChineseMedicineDevelopments({
          populate: "*"
        })

        if (response && response.data) {
          // Log the Chinese medicine data to debug the structure
          console.log("Chinese medicine data:", JSON.stringify(response.data, null, 2));

          // Extract Chinese medicine info from the response
          const info = Array.isArray(response.data) ? response.data : [response.data];

          if (info.length > 0) {
            setMedicineInfo(info);
            setDebugInfo(`Found ${info.length} Chinese medicine information items.`);
          } else {
            // If no info is found, use fallback data
            setMedicineInfo(fallbackInfo);
            setDebugInfo("No Chinese medicine information found in API response. Using fallback data.");
          }
        } else {
          // If response is empty, use fallback data
          setMedicineInfo(fallbackInfo);
          setDebugInfo("Empty API response. Using fallback data.");
        }

        setLoading(false)
      } catch (err) {
        console.error("Error loading Chinese medicine info:", err)
        setError("Failed to load Chinese medicine information. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setMedicineInfo(fallbackInfo)
        setDebugInfo(`API error: ${err}. Using fallback data.`);
      }
    }

    loadMedicineInfo()
  }, [])

  // Helper function to get image URL
  const getImageUrl = (info: ChineseMedicine) => {
    if (!info.image) return "/placeholder.svg";
    
    if (Array.isArray(info.image) && info.image.length > 0) {
      const firstImage = info.image[0];
      return firstImage.formats?.medium?.url || 
             firstImage.formats?.small?.url || 
             firstImage.url || 
             "/placeholder.svg";
    }
    
    // Handle single image case
    const singleImage = info.image as {
      formats?: {
        medium?: { url: string },
        small?: { url: string }
      },
      url: string
    };
    
    return singleImage.formats?.medium?.url || 
           singleImage.formats?.small?.url || 
           singleImage.url || 
           "/placeholder.svg";
  }

  // Helper function to get image alt text
  const getImageAlt = (info: ChineseMedicine) => {
    if (!info.image) return info.title || "Chinese Medicine Information";
    
    if (Array.isArray(info.image) && info.image.length > 0) {
      return info.image[0].alternativeText || info.title || "Chinese Medicine Information";
    }
    
    // Handle single image case
    const singleImage = info.image as {
      alternativeText: string | null
    };
    
    return singleImage.alternativeText || info.title || "Chinese Medicine Information";
  }

  // Helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      return content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      });
    }

    return null;
  };

  // Add a new function to get all images for the slider
  const getAllImages = (info: ChineseMedicine) => {
    if (Array.isArray(info.image)) {
      return info.image;
    }
    if (info.images && info.images.length > 0) {
      return info.images;
    }
    if (info.image) {
      // Convert single image to array format
      const singleImage = info.image as {
        id: number,
        name: string,
        alternativeText: string | null,
        formats: {
          large?: { url: string, width: number, height: number },
          medium?: { url: string, width: number, height: number },
          small?: { url: string, width: number, height: number },
          thumbnail?: { url: string, width: number, height: number }
        },
        url: string
      };
      return [singleImage];
    }
    return [];
  }

  // Fallback data in case the API fails
  const fallbackInfo: ChineseMedicine[] = [
    {
      id: 1,
      documentId: "fallback-1",
      title: "Advancements in Traditional Chinese Medicine (TCM) and Its Integration with Modern Healthcare",
      content: [
        {
          type: "paragraph",
          children: [
            {
              text: "The evolution of TCM has been marked by significant developments in:",
              type: "text"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              text: "Herbal Medicine Research: Advances in isolating active compounds from traditional herbs such as ginseng, astragalus, and licorice.",
              type: "text"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              text: "Acupuncture & Neurological Benefits: Clinical studies showing acupuncture's impact on pain relief, stress reduction, and neuroplasticity.",
              type: "text"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              text: "Integration with Modern Medicine: Efforts to combine TCM with Western medicine for treating chronic diseases such as diabetes and cardiovascular conditions.",
              type: "text"
            }
          ]
        }
      ],
      category: "Healthcare & Medical Science",
      publicationDate: "2025-03-01",
      createdAt: "2023-12-05T00:00:00.000Z",
      updatedAt: "2023-12-05T00:00:00.000Z",
      publishedAt: "2023-12-05T00:00:00.000Z",
      locale: "en"
    },
    {
      id: 2,
      documentId: "fallback-2",
      title: "中醫藥標準化國際論壇在香港舉行",
      content: [
        {
          type: "paragraph",
          children: [
            {
              text: "由香港貿易發展局和世界中醫藥學會聯合會共同主辦的「中醫藥標準化國際論壇」在香港會議展覽中心舉行。論壇匯聚來自世界各地的中醫藥專家，探討中醫藥標準化的最新發展和國際合作機遇。",
              type: "text"
            }
          ]
        }
      ],
      category: "行業活動",
      publicationDate: "2023-11-15",
      externalLink: "https://www.hktdc.com/",
      createdAt: "2023-11-15T00:00:00.000Z",
      updatedAt: "2023-11-15T00:00:00.000Z",
      publishedAt: "2023-11-15T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 3,
      documentId: "fallback-3",
      title: "香港中醫藥科技園開幕",
      content: [
        {
          type: "paragraph",
          children: [
            {
              text: "香港中醫藥科技園正式開幕，園區設有先進的中醫藥研發設施、臨床試驗中心和產業孵化基地。科技園旨在促進中醫藥現代化，推動香港成為中醫藥創新研發中心。",
              type: "text"
            }
          ]
        }
      ],
      category: "產業發展",
      publicationDate: "2023-10-20",
      externalLink: "https://www.hkstp.org/",
      createdAt: "2023-10-20T00:00:00.000Z",
      updatedAt: "2023-10-20T00:00:00.000Z",
      publishedAt: "2023-10-20T00:00:00.000Z",
      locale: "zh"
    }
  ]

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "中醫中藥發展資訊",
          en: "Chinese Medicine Development Information",
        }}
        description={{
          zh: "中醫中藥發展的最新資訊",
          en: "Latest information on the development of Chinese medicine",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">中醫中藥發展資訊 / Chinese Medicine Development Information</h2>
              <p>
                九龍總商會關注中醫中藥行業發展，定期收集和分享中醫中藥領域的
                最新政策、行業動態、研究成果和市場趨勢等資訊，為會員提供參考，
                促進中醫中藥產業的發展和創新。
              </p>
              <p>
                The Kowloon Chamber of Commerce pays attention to the development of the
                Chinese medicine industry, regularly collects and shares the latest policies,
                industry dynamics, research results, and market trends in the field of Chinese medicine,
                providing references for members and promoting the development and innovation of
                the Chinese medicine industry.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
            <p>{error}</p>
            {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
          </div>
        )}

        {loading ? (
          // Loading skeleton
          <div className="space-y-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-3 gap-0">
                    <Skeleton className="h-64 w-full" />
                    <div className="md:col-span-2 p-6 md:p-8">
                      <Skeleton className="h-8 w-3/4 mb-3" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-2/3 mb-6" />

                      <div className="grid sm:grid-cols-2 gap-4 mb-6">
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-full sm:col-span-2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-8">
            {medicineInfo.length > 0 ? (
              medicineInfo.map((info) => (
                <Card key={info.id} className="overflow-hidden hover:shadow-lg transition-all">
                  <CardContent className="p-0">
                    <div className="grid md:grid-cols-3 gap-0">
                      <div className="relative aspect-video">
                        {getAllImages(info).length > 1 ? (
                          <ImageSlider
                            images={getAllImages(info)}
                            alt={getImageAlt(info)}
                            interval={2000}
                          />
                        ) : (
                          <Image
                            src={getImageUrl(info)}
                            alt={getImageAlt(info)}
                            fill
                            className="object-cover"
                            onError={(e) => {
                              console.error("Error loading image");
                              (e.target as HTMLImageElement).src = "/placeholder.svg";
                            }}
                          />
                        )}
                      </div>
                      <div className="md:col-span-2 p-6 md:p-8">
                        <h3 className="text-2xl font-bold mb-3">{info.title}</h3>

                        <div className="text-muted-foreground mb-6">
                          {renderStructuredContent(info.content)}
                        </div>

                        <div className="grid sm:grid-cols-2 gap-4 mb-6">
                          {info.publicationDate && (
                            <div className="flex items-center gap-2">
                              <Calendar className="h-5 w-5 text-primary" />
                              <span>Published: {info.publicationDate}</span>
                            </div>
                          )}
                          {info.category && (
                            <div className="flex items-center gap-2">
                              <Tag className="h-5 w-5 text-primary" />
                              <span>Category: {info.category}</span>
                            </div>
                          )}
                        </div>

                        {info.externalLink && (
                          <Button asChild variant="outline" className="mt-2">
                            <a href={info.externalLink} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                              <ExternalLink className="h-4 w-4" />
                              查看原文
                            </a>
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <p className="text-xl text-muted-foreground">暫無中醫中藥發展資訊 / No Chinese medicine information available</p>
                {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}