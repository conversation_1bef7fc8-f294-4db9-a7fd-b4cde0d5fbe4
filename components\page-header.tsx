import Link from "next/link"
import { ChevronRight } from "lucide-react"

interface LocalizedText {
  zh: string
  en: string
}

interface Breadcrumb {
  title: LocalizedText
  href: string
}

interface PageHeaderProps {
  title: LocalizedText
  subtitle?: LocalizedText
  breadcrumbs?: Breadcrumb[]
}

export default function PageHeader({ title, subtitle, breadcrumbs }: PageHeaderProps) {
  return (
    <div className="space-y-4">
      {breadcrumbs && breadcrumbs.length > 0 && (
        <nav className="flex text-sm text-gray-500">
          <ol className="flex items-center flex-wrap">
            {breadcrumbs.map((breadcrumb, index) => (
              <li key={breadcrumb.href} className="flex items-center">
                {index > 0 && <ChevronRight className="h-4 w-4 mx-2" />}
                {index === breadcrumbs.length - 1 ? (
                  <span className="text-gray-900">{breadcrumb.title.zh} / {breadcrumb.title.en}</span>
                ) : (
                  <Link href={breadcrumb.href} className="hover:underline">
                    {breadcrumb.title.zh} / {breadcrumb.title.en}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </nav>
      )}
      
      <div>
        <h1 className="text-3xl font-bold">{title.zh} / {title.en}</h1>
        {subtitle && (
          <p className="text-lg text-gray-600 mt-2">{subtitle.zh} / {subtitle.en}</p>
        )}
      </div>
    </div>
  )
}
