"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { fetchCouncilMembers } from "@/lib/strapi"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp, ChevronLeft, ChevronRight } from "lucide-react"
import AnimatedHero from "@/components/animated-hero"
import Image from "next/image"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { useRef } from "react"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId?: string
  name?: string
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: StrapiImageFormats
  url?: string
  isVideo?: boolean
  previewUrl?: string | null
  mime?: string
}

interface CouncilMember {
  id: number
  documentId?: string
  title?: string
  name?: string
  position?: string
  biography?: any[]
  description?: any[]
  order?: number
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  locale?: string
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

interface StrapiResponse {
  data: Array<{
    id: number
    attributes?: CouncilMember
  } & CouncilMember>
  meta?: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

function ManagementTeamContent() {
  const searchParams = useSearchParams()
  const [members, setMembers] = useState<CouncilMember[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedMembers, setExpandedMembers] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (memberId: number) => {
    setExpandedMembers(prev => {
      const newSet = new Set(prev)
      if (newSet.has(memberId)) {
        newSet.delete(memberId)
      } else {
        newSet.add(memberId)
      }
      return newSet
    })
  }

  useEffect(() => {
    const loadMembers = async () => {
      try {
        setLoading(true)
        const locale = getStrapiLocale

        console.log("Management page - Current language:", currentLang)
        console.log("Management page - Strapi locale:", locale)

        const response = await fetchCouncilMembers({
          populate: "*",
          sort: "order:asc",
          locale: locale
        }) as StrapiResponse

        console.log("Management page - API response:", response)

        // Process the data similar to news page
        const processedData = response.data.map(item => {
          const processedItem = { ...item };
          if (item.attributes) {
            Object.assign(processedItem, item.attributes);
            processedItem.id = item.id;

            // Handle image data from Strapi v4 format
            if (item.attributes.image && (item.attributes.image as any).data) {
              const imageData = (item.attributes.image as any).data;
              if (Array.isArray(imageData)) {
                processedItem.images = imageData.map((img: any) => ({
                  id: img.id,
                  ...img.attributes || img
                }));
              } else {
                processedItem.image = {
                  id: imageData.id,
                  ...imageData.attributes || imageData
                };
              }
            }
          } else if (item.image && Array.isArray(item.image) && item.image.length > 0) {
            processedItem.images = item.image.map(img => ({
              id: img.id,
              url: img.url,
              formats: img.formats
            }));
          }
          return processedItem;
        });

        setMembers(processedData)
        console.log("Management page - Processed data:", processedData)

      } catch (err: any) {
        console.error("Error fetching council members:", err)

        // Provide more detailed error information
        let errorMessage = t('management.error');
        if (err.message.includes('403')) {
          errorMessage = "API access forbidden. Please check if the council-members endpoint is properly configured in Strapi with correct permissions.";
        } else if (err.message.includes('404')) {
          errorMessage = "Council members endpoint not found. Please verify the API endpoint exists.";
        } else if (err.message.includes('Failed to fetch')) {
          errorMessage = "Network error. Please check your internet connection and try again.";
        }

        setError(errorMessage)
      } finally {
        setLoading(false)
      }
    }

    loadMembers()
  }, [currentLang]) // Only re-run when the actual language changes

  if (error) {
    return (
      <div className="container py-12">
        <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
          <h2 className="text-xl font-semibold text-red-700 mb-2">
            <span className="block">發生錯誤</span>
            <span className="text-base text-red-600">An error occurred</span>
          </h2>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-12 space-y-8">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('management.teamTitle')}</h2>
            <p>
              {t('management.teamDescription')}
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          // Loading skeletons
          Array(6).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-video bg-muted animate-pulse" />
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
                  <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
                  <div className="space-y-2">
                    <div className="h-2 bg-muted rounded animate-pulse" />
                    <div className="h-2 bg-muted rounded animate-pulse w-5/6" />
                    <div className="h-2 bg-muted rounded animate-pulse w-4/6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : members.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-muted-foreground">{t('management.noMembers')}</p>
          </div>
        ) : (
          members.map((member) => {
            const isExpanded = expandedMembers.has(member.id)
            const memberName = member.name || member.title || `Member ${member.id}`
            const memberPosition = member.position || ""

            // Handle images - check if member has images array or single image
            const memberImages = member.images || (member.image && Array.isArray(member.image) ? member.image : member.image ? [member.image] : [])

            return (
              <Card key={member.id} className="overflow-hidden">
                <CardContent className="p-0">
                  {(() => {
                    const mediaList = patchMediaMime(Array.isArray(member.image) ? member.image : (member.image ? [member.image] : []));

                    if (mediaList.length > 1) {
                      return <FullImageSlider images={mediaList} alt={memberName || "Council Member Media"} interval={3000} />
                    } else if (mediaList.length === 1) {
                      return (
                        <div className="relative aspect-[4/3] w-full min-h-[1px]">
                          {mediaList[0].mime?.startsWith('video/') ? (
                            <video
                              src={mediaList[0].url}
                              poster={mediaList[0].previewUrl}
                              controls
                              className="w-full h-full object-contain"
                              playsInline
                            />
                          ) : (
                            <Image
                              src={getImageUrl(mediaList[0])}
                              alt={memberName || "Council Member Image"}
                              fill
                              className="object-contain"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          )}
                        </div>
                      )
                    } else {
                      return (
                        <div className="flex items-center justify-center aspect-[4/3] bg-gray-100">
                          <p className="text-muted-foreground p-4 text-center">No media available</p>
                        </div>
                      )
                    }
                  })()}
                </CardContent>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold">{memberName}</h3>
                  {memberPosition && (
                    <p className="text-muted-foreground font-medium">{memberPosition}</p>
                  )}

                  {/* Biography/Description */}
                  {(member.biography || member.description) && (
                    <div className="prose prose-sm max-w-none">
                      {(member.biography || member.description)?.slice(0, isExpanded ? undefined : 2).map((block: any, index: number) => {
                        if (block.type === "paragraph") {
                          return (
                            <p key={index} className="text-sm text-muted-foreground mb-2">
                              {block.children?.map((child: any) => child.text).join("") || ""}
                            </p>
                          )
                        }
                        return null
                      })}

                      {(member.biography || member.description) && (member.biography || member.description)!.length > 2 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(member.id)}
                          className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                        >
                          {isExpanded ? (
                            <>
                              {t('management.readLess')} <ChevronUp className="ml-1 h-3 w-3" />
                            </>
                          ) : (
                            <>
                              {t('management.readMore')} <ChevronDown className="ml-1 h-3 w-3" />
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })
        )}
      </div>
    </div>
  )
}

function ManagementTeamLoading() {
  return (
    <div className="min-h-screen">
      <div className="relative h-96 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="h-8 bg-white/20 rounded animate-pulse w-64 mx-auto mb-4" />
          <div className="h-4 bg-white/20 rounded animate-pulse w-96 mx-auto" />
        </div>
      </div>

      <div className="container py-12 space-y-8">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="h-6 bg-muted rounded animate-pulse w-48 mx-auto" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                <div className="h-4 bg-muted rounded animate-pulse w-4/6 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array(6).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-video bg-muted animate-pulse" />
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
                  <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
                  <div className="space-y-2">
                    <div className="h-2 bg-muted rounded animate-pulse" />
                    <div className="h-2 bg-muted rounded animate-pulse w-5/6" />
                    <div className="h-2 bg-muted rounded animate-pulse w-4/6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Component that uses searchParams
function ManagementTeamPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="management.title"
        description="management.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<ManagementTeamLoading />}>
        <ManagementTeamContent />
      </Suspense>
    </div>
  )
}

// Main export with Suspense wrapper
export default function ManagementTeam() {
  return (
    <Suspense fallback={<ManagementTeamLoading />}>
      <ManagementTeamPage />
    </Suspense>
  )
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )}

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Helper function to get image URL from Strapi image object
const getImageUrl = (strapiImage: StrapiImage | undefined): string => {
  if (!strapiImage) return "/placeholder.svg";

  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

  // For videos, return the preview URL if available
  if (strapiImage.mime?.startsWith('video/')) {
    return strapiImage.previewUrl || "/placeholder.svg";
  }

  // For images, try different format sizes
  if (strapiImage.formats?.medium?.url) {
    return strapiImage.formats.medium.url.startsWith('http')
      ? strapiImage.formats.medium.url
      : `${strapiUrl}${strapiImage.formats.medium.url}`;
  }
  if (strapiImage.formats?.small?.url) {
    return strapiImage.formats.small.url.startsWith('http')
      ? strapiImage.formats.small.url
      : `${strapiUrl}${strapiImage.formats.small.url}`;
  }
  if (strapiImage.url) {
    return strapiImage.url.startsWith('http')
      ? strapiImage.url
      : `${strapiUrl}${strapiImage.url}`;
  }
  // Fallback to placeholder
  return "/placeholder.svg";
}