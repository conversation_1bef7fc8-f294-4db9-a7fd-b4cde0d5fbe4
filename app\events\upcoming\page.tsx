"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { useUser } from "@/contexts/user-context"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp, Calendar, MapPin, Clock, Globe, ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"

import AnimatedH<PERSON> from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { fetchUpcomingEvents } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText?: string
  caption?: string
  width: number | null
  height: number | null
  formats?: StrapiImageFormats
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl?: string
  provider: string
  provider_metadata?: any
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale?: string
  isVideo?: boolean
}

interface UpcomingEvent {
  id: number
  documentId: string
  title: string
  date?: string
  location?: string
  description?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  registrationLink?: string
  order?: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: StrapiImage | StrapiImage[] | null
  images?: StrapiImage[]
  seo?: any
  localizations?: any[]
}

// Loading component
function UpcomingEventsLoading() {
  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4 mx-auto" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </CardContent>
      </Card>

      <div className="space-y-8">
        {Array(3).fill(0).map((_, index) => (
          <Card key={index}>
            <CardContent className="p-8">
              <div className="grid md:grid-cols-2 gap-8 items-start">
                <div className="space-y-4">
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-10 w-3/4" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
                <Skeleton className="h-64 w-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Local FullImageSlider copied from New Era page for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: StrapiImage[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Main content component
function UpcomingEventsContent() {
  const { user } = useUser()
  const searchParams = useSearchParams()
  const [upcomingEvents, setUpcomingEvents] = useState<UpcomingEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null)
  const [expandedEvents, setExpandedEvents] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (eventId: number) => {
    setExpandedEvents(prev => {
      const newSet = new Set(prev)
      if (newSet.has(eventId)) {
        newSet.delete(eventId)
      } else {
        newSet.add(eventId)
      }
      return newSet
    })
  }

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae'

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/upcoming-events`)
        url.searchParams.append('populate', '*')
        if (getStrapiLocale) {
          url.searchParams.append('locale', getStrapiLocale)
        }

        console.log('Upcoming Events - API URL:', url.toString())

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log('Upcoming Events - API Response:', result)

        if (result && result.data) {
          const eventsData = result.data as UpcomingEvent[]

          // Sort the events by date if available
          const sortedEvents = [...eventsData].sort((a, b) => {
            if (!a.date && !b.date) return 0
            if (!a.date) return 1
            if (!b.date) return -1
            return new Date(a.date).getTime() - new Date(b.date).getTime() // Earliest first for upcoming events
          })

          setUpcomingEvents(sortedEvents)
        } else {
          console.error('Upcoming events data not found in response:', result)
          setError(t('upcomingEvents.noEvents'))
        }
      } catch (error: any) {
        console.error('Error fetching upcoming events data:', error)
        setError(t('upcomingEvents.error'))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Helper function to render rich text content
  const renderRichText = (content: any[] | string) => {
    if (typeof content === 'string') {
      return <p className="mb-4">{content}</p>
    }

    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('upcomingEvents.noContent')}</p>
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children?.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null
    })
  }

  // Helper function to get event images and videos
  const getEventImages = (event: UpcomingEvent): StrapiImage[] => {
    const media: StrapiImage[] = []
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'

    // Check if image is an array (multiple media items)
    if (Array.isArray(event.image)) {
      media.push(...event.image.map(img => ({
        ...img,
        url: img.url?.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
        isVideo: img.mime?.startsWith('video/') || false
      })))
    } else if (event.image) {
      // Single media item
      media.push({
        ...event.image,
        url: event.image.url?.startsWith('http') ? event.image.url : `${strapiUrl}${event.image.url}`,
        isVideo: event.image.mime?.startsWith('video/') || false
      })
    }

    // Also check images field
    if (event.images && Array.isArray(event.images)) {
      media.push(...event.images.map(img => ({
        ...img,
        url: img.url?.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
        isVideo: img.mime?.startsWith('video/') || false
      })))
    }

    // Filter out any images without a valid url
    return media.filter(img => !!img.url)
  }

  // Helper function to get media URL with fallback
  const getImageUrl = (media: StrapiImage | undefined): string => {
    if (!media) return "/placeholder.svg"

    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'

    // For videos, return the preview URL if available
    if (media.mime?.startsWith('video/')) {
      return media.previewUrl || "/placeholder.svg"
    }

    // For images, try different format sizes
    if (media.formats?.medium?.url) {
      return media.formats.medium.url.startsWith('http')
        ? media.formats.medium.url
        : `${strapiUrl}${media.formats.medium.url}`
    }
    if (media.formats?.small?.url) {
      return media.formats.small.url.startsWith('http')
        ? media.formats.small.url
        : `${strapiUrl}${media.formats.small.url}`
    }
    if (media.url) {
      return media.url.startsWith('http')
        ? media.url
        : `${strapiUrl}${media.url}`
    }

    return "/placeholder.svg"
  }

  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('upcomingEvents.staticTitle')}</h2>
            <p>{t('upcomingEvents.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-8">
        {loading ? (
          // Loading skeletons
          Array(3).fill(0).map((_, index) => (
            <Card key={index}>
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-start">
                  <div className="space-y-4">
                    <Skeleton className="h-8 w-24" />
                    <Skeleton className="h-10 w-3/4" />
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                  <Skeleton className="h-64 w-full" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : error ? (
          <Card>
            <CardContent className="p-8">
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
              </div>
            </CardContent>
          </Card>
        ) : upcomingEvents.length > 0 ? (
          upcomingEvents.map((event) => {
            const isExpanded = expandedEvents.has(event.id)
            const images = getEventImages(event)

            // Determine if content is long enough to show read more/less
            const hasLongContent = (typeof event.description === 'string' && event.description.length > 200) ||
                                 (Array.isArray(event.description) && event.description.length > 2)

            return (
              <Card key={event.id} className="overflow-hidden">
                <CardContent className="p-8">
                  <div className="grid md:grid-cols-2 gap-8 items-start">
                    <div className="space-y-4">
                      {event.date && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                          <Calendar className="h-4 w-4" />
                          <time>{new Date(event.date).toLocaleDateString()}</time>
                        </div>
                      )}
                      <h1 className="text-3xl font-bold mb-4">{event.title}</h1>

                      {event.location && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                          <MapPin className="h-4 w-4" />
                          <span>{event.location}</span>
                        </div>
                      )}

                      {/* Description */}
                      {event.description && (
                        <div className={`space-y-4 ${!isExpanded && hasLongContent ? 'line-clamp-3' : ''}`}>
                          {renderRichText(event.description)}
                        </div>
                      )}

                      {/* Read More/Less Button */}
                      {hasLongContent && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(event.id)}
                          className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                        >
                          {isExpanded ? (
                            <>
                              {t('upcomingEvents.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                            </>
                          ) : (
                            <>
                              {t('upcomingEvents.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                            </>
                          )}
                        </Button>
                      )}

                      {/* Action Buttons */}
                      <div className="flex flex-wrap gap-4 pt-4">
                        {event.registrationLink && (
                          <>
                            <Button
                              variant="outline"
                              onClick={() => {
                                let url = event.registrationLink || '';
                                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                                  url = `https://${url}`;
                                }
                                window.open(url, '_blank');
                              }}
                            >
                              <Globe className="h-5 w-5 mr-2" />
                              {t('upcomingEvents.visitWebsite')}
                            </Button>
                            <Button
                              onClick={() => {
                                if (!user) {
                                  setSelectedEvent(event.title);
                                  setShowLoginDialog(true);
                                } else {
                                  window.location.href = `/events/register?eventName=${encodeURIComponent(event.title)}&email=${encodeURIComponent(user.email)}`;
                                }
                              }}
                            >
                              <Clock className="h-5 w-5 mr-2" />
                              {t('upcomingEvents.registerNow')}
                            </Button>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Image/Video Section - Right Side Only, No UI Change */}
                    {(() => { console.log("Slider images for event", event.title, images); return null })()}
                    <div className="relative w-full aspect-[4/3] rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center">
                      {images.length > 0 ? (
                        images.length > 1 ? (
                          <FullImageSlider
                            images={images}
                            alt={event.title || "Upcoming Event"}
                            interval={3000}
                          />
                        ) : (
                          images[0].mime?.startsWith('video/') ? (
                            <video
                              src={images[0].url}
                              poster={images[0].previewUrl}
                              controls
                              className="w-full h-full object-contain"
                              playsInline
                            />
                          ) : (
                            <Image
                              src={getImageUrl(images[0])}
                              alt={`${event.title} - Upcoming Event`}
                              fill
                              className="object-contain"
                              sizes="(max-width: 768px) 100vw, 50vw"
                            />
                          )
                        )
                      ) : (
                        <div className="flex items-center justify-center w-full h-full bg-gray-100">
                          <p className="text-muted-foreground p-4 text-center">No media available</p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        ) : (
          <Card>
            <CardContent className="p-8">
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('upcomingEvents.noEvents')}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Login Dialog */}
      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('upcomingEvents.loginRequired')}</DialogTitle>
            <DialogDescription>
              {t('upcomingEvents.loginRequiredDesc')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowLoginDialog(false)}
            >
              {t('upcomingEvents.cancel')}
            </Button>
            <Button
              onClick={() => {
                const returnUrl = encodeURIComponent(`/events/register?eventName=${encodeURIComponent(selectedEvent || '')}`);
                window.location.href = `/login?returnUrl=${returnUrl}`;
              }}
            >
              {t('upcomingEvents.loginNow')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Component that uses searchParams - wrapped in Suspense
function UpcomingEventsPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="upcomingEvents.title"
        description="upcomingEvents.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<UpcomingEventsLoading />}>
        <UpcomingEventsContent />
      </Suspense>
    </div>
  )
}

// Main page component
export default function UpcomingEventsPage() {
  return (
    <Suspense fallback={<UpcomingEventsLoading />}>
      <UpcomingEventsPageContent />
    </Suspense>
  )
}