import { NextResponse } from "next/server"

export async function GET(request: Request) {
  try {
    // Get URL and token from query params or environment variables
    const { searchParams } = new URL(request.url)
    const urlParam = searchParams.get("url")
    const tokenParam = searchParams.get("token")

    const strapiUrl = urlParam || process.env.STRAPI_URL
    const strapiToken = tokenParam || process.env.STRAPI_TOKEN

    if (!strapiUrl || !strapiToken) {
      return NextResponse.json({ error: "Missing Strapi configuration" }, { status: 400 })
    }

    // Test different URL variations to find the correct API endpoint
    const urlVariations = [
      { name: "Original URL", url: strapiUrl },
      { name: "Without trailing slash", url: strapiUrl.replace(/\/$/, "") },
      { name: "API path", url: `${strapiUrl.replace(/\/$/, "")}/api` },
      { name: "API path without double /api", url: strapiUrl.replace(/\/api\/?$/, "") + "/api" },
    ]

    const results = []

    for (const variation of urlVariations) {
      try {
        console.log(`Testing URL variation: ${variation.name} - ${variation.url}`)

        const response = await fetch(variation.url, {
          headers: {
            Authorization: `Bearer ${strapiToken}`,
          },
          cache: "no-store",
        })

        const result = {
          name: variation.name,
          url: variation.url,
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
        }

        try {
          const responseText = await response.text()
          result.contentType = response.headers.get("content-type")

          // Check if it's JSON
          if (result.contentType?.includes("application/json")) {
            try {
              const data = JSON.parse(responseText)
              result.isJson = true
              result.data = data
            } catch (e) {
              result.isJson = false
              result.jsonError = String(e)
            }
          } else {
            result.isJson = false
            result.preview = responseText.substring(0, 100) + (responseText.length > 100 ? "..." : "")
          }
        } catch (textError) {
          result.error = String(textError)
        }

        results.push(result)
      } catch (error) {
        results.push({
          name: variation.name,
          url: variation.url,
          error: String(error),
        })
      }
    }

    // Find the best URL variation (one that returns JSON)
    let bestUrlVariation = null
    for (const result of results) {
      if (result.isJson) {
        bestUrlVariation = result.url
        break
      }
    }

    // If we found a good URL, test some common content types
    const contentTypeResults = []
    if (bestUrlVariation) {
      const contentTypes = ["/articles", "/news-articles", "/events", "/publications", "/news", "/posts"]

      for (const contentType of contentTypes) {
        try {
          const endpoint = `${bestUrlVariation}${contentType}`
          console.log(`Testing content type: ${endpoint}`)

          const response = await fetch(endpoint, {
            headers: {
              Authorization: `Bearer ${strapiToken}`,
            },
            cache: "no-store",
          })

          const result = {
            contentType,
            endpoint,
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
          }

          if (response.ok) {
            try {
              const responseText = await response.text()
              result.contentType = response.headers.get("content-type")

              if (result.contentType?.includes("application/json")) {
                try {
                  const data = JSON.parse(responseText)
                  result.isJson = true
                  result.data = data
                } catch (e) {
                  result.isJson = false
                  result.jsonError = String(e)
                }
              } else {
                result.isJson = false
                result.preview = responseText.substring(0, 100) + (responseText.length > 100 ? "..." : "")
              }
            } catch (textError) {
              result.error = String(textError)
            }
          }

          contentTypeResults.push(result)
        } catch (error) {
          contentTypeResults.push({
            contentType,
            error: String(error),
          })
        }
      }
    }

    return NextResponse.json({
      urlVariations: results,
      bestUrlVariation,
      contentTypeResults,
      recommendation: bestUrlVariation
        ? `Use ${bestUrlVariation} as your STRAPI_URL environment variable`
        : "Could not find a valid Strapi API URL",
    })
  } catch (error) {
    console.error("Error in API route:", error)
    return NextResponse.json({ error: String(error) }, { status: 500 })
  }
}

